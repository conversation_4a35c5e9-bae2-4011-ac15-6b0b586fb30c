/// <reference types="vitest" />

import { type ConfigEnv, type UserConfigExport, loadEnv } from "vite"
import path, { resolve } from "path"
import vue from "@vitejs/plugin-vue"
import { createSvgIconsPlugin } from "vite-plugin-svg-icons"
import svgLoader from "vite-svg-loader"
import DefineOptions from "unplugin-vue-define-options/vite"
import ViteRestart from "vite-plugin-restart"

import { defineConfig } from 'vite'
import legacy from '@vitejs/plugin-legacy'
import babel from 'vite-plugin-babel'

/** 配置项文档：https://cn.vitejs.dev/config */
export default (configEnv : ConfigEnv) : UserConfigExport => {
  const viteEnv = loadEnv(configEnv.mode, process.cwd()) as ImportMetaEnv
  const { VITE_PUBLIC_PATH } = viteEnv
  return {
    /** 打包时根据实际情况修改 base */
    base: VITE_PUBLIC_PATH,
    resolve: {
      alias: {
        /** @ 符号指向 src 目录 */
        "@": resolve(__dirname, "./src")
      }
    },
    server: {
      /** 是否开启 HTTPS, undefined为不开启 */
      https: undefined,
      /** 设置 host: true 才可以使用 Network 的形式，以 IP 访问项目 */
      host: true, // host: "0.0.0.0"
      /** 端口号 */
      port: 3333,
      /** 是否自动打开浏览器 */
      open: false,
      /** 跨域设置允许 */
      cors: true,
      /** 端口被占用时，是否直接退出 */
      strictPort: false,
      /** 接口代理 */
      proxy: {
        "/webapi": {
          // target: "https://api.xiaoyeoo.com",    //线上
          // target: "https://ai-test.xiaoyeoo.com",  //测试
          // target: "http://************:9001/",
		      //  target: "http://************:9004/",
          // target: "https://api-dev.xiaoyeoo.com",    //开发
               target:  "http://************:2000/",   //张宇开发
		  
          ws: true,
          /** 是否允许跨域 */
          changeOrigin: true
        },
        "/api": {
          // target: "https://api-dev.xiaoyeoo.com",     //开发
          // target: "https://ai-test.xiaoyeoo.com",    //测试
          // target: "http://*************:2000/",
		      // target: "http://************:9004/",
          // target: "https://api.xiaoyeoo.com",   //线上
          target:  "http://************:2000/",  //张宇开发
          ws: true,
          /** 是否允许跨域 */
          changeOrigin: true
        }
      }
    },
    build: {
      sourcemap: false,
      emptyOutDir: true, // 构建前清空 dist
      /** 消除打包大小超过 500kb 警告 */
      chunkSizeWarningLimit: 2000,
      /** Vite 2.6.x 以上需要配置 minify: "terser", terserOptions 才能生效 */
      minify: "esbuild",
      /** 在打包代码时移除 console.log、debugger 和 注释 */
      terserOptions: {
        compress: {
          drop_console: false,
          drop_debugger: true,
          pure_funcs: ["console.log"]
        },
        format: {
          /** 删除注释 */
          comments: false
        }
      },
      // rollupOptions: {
      //   output: {
      //     // 确保文件名带哈希，避免缓存问题
      //     chunkFileNames: "assets/[name]-[hash].js",
      //     assetFileNames: "assets/[name]-[hash][extname]"
      //   }
      // },
      /** 打包后静态资源目录 */
      assetsDir: "static"
    },
    /** Vite 插件 */
    plugins: [
      vue(),
      /** 将 SVG 静态图转化为 Vue 组件 */
      svgLoader({ defaultImport: "url" }),
      /** SVG */
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), "src/assets/icons/svg")],
        symbolId: "icon-[dir]-[name]"
      }),
      /** DefineOptions 可以更简单的注册组件名称 */
      DefineOptions(),
      /** 自动按需引入 (已更改为完整引入，所以注释了) */
      ViteRestart({
        restart: ["vite.config.js"]
      }),
      /** 监听vite.config.js 文件修改，不用重新启动项目，一般来说修改vite配置文件和环境配置文件都是需要重新启动的，需要什么文件启动就加在该插件里面 */
      babel({
        babelConfig: {
          plugins: ['@babel/plugin-proposal-optional-chaining']
        }
      }),
      legacy({
        targets: ['chrome >= 78'],
        modernPolyfills: true
      })
    ]
      // plugins: [
      //   babel({
      //     babelConfig: {
      //       plugins: ['@babel/plugin-proposal-optional-chaining']
      //     }
      //   }),
      //   legacy({
      //     targets: ['chrome >= 78'],
      //     modernPolyfills: true
      //   })
      // ],


  }
}