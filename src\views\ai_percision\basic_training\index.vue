<template>
          <!-- 顶部导航 -->
    <header class="page-header">
      <div class="breadcrumbs">
        <a href="#" class="back-link" @click.prevent="goBack">&lt; 返回</a>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item">AI精准学</span>{{ learnUsers[0]?.gradeId || '' }}
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item active" > {{ query.type=='1' ?'基础训练':'提升训练' }}</span>
      </div>
    </header>
  <div class="training-page">
    
    <div class="left-sidebar" v-loading="loading">
      <knowledgeTree
         ref="knowledgeTreeRef"
        :selected="chapterId"
        :iswen="true"
        :options="options"
        :showCurrentTaskIndicator="!!routeHasChapterId"
        currentTaskImageUrl="@/assets/img/synchronous/task-badge.png"
        @setChapterId="setChapterId"
        :defaultExpandAll="true"
      />
      <div class="sidebar-title">
        选择章节
        <span class="title-decoration"></span>
      </div>
    </div>

    <div class="main-content" v-loading="loading2">
      <div class="content-head">
        <div class="head-body">
          <img src="@/assets/img/percision/training/textbook.png" alt="" class="textbook" />
          <div class="head-title">
            当前教材：{{ bookVersionName }}
          </div>
          <div @click="onModify" class="head-switch">切换教材</div>
        </div>

        <img @click="onMark" src="@/assets/img/percision/training/superficiality.png" alt="" class="superficiality" />
        <div class="catalogue">
          <span>{{ chapterPathText }}{{ chapterName }}</span>
          <img src="@/assets/img/percision/training/dsj.png" alt="">
        </div>      
            
      </div>

      <div class="content-tip">
        <div class="tip-content">
          <img src="@/assets/img/percision/training/mty.png" alt="" class="tip-avatar" />
          <div class="tip-text">本节为老师布置的任务，请在规定时间内完成。<span style="color:#DD2A2A">截止时间：2025/05/15</span></div>
        </div>
        <img src="@/assets/img/percision/training/tip_bg.png" class="tip-bg" alt="" />
      </div>
      <!-- 知识点训练列表 -->
      <div class="knowledge-training-list" v-if="pageStatus">
        <div class="lesson-section" v-if="lessonData.length > 0">

          <div class="knowledge-info">
            <div class="knowledge-box">
              <!-- 根据年级动态分配知识点数量：年级>6显示3个，否则显示2个 -->
              <!-- 当前年级: {{ learnUsers[0]?.gradeId || 0 }}, 每课知识点数: {{ pointsPerLesson }} -->
              <div 
                class="lesson-container" 
                v-for="(_, lessonIndex) in totalLessons" 
                :key="'lesson-' + lessonIndex"
              >
                <div class="lesson-header">
                  <div class="lesson-title">第{{ lessonIndex + 1 }}课</div>
                </div>
                
                <div class="lesson-content">
                  <!-- 遍历当前课的知识点 -->
              <div 
                class="knowledge-item" 
                    v-for="knowledge in getLessonKnowledgePoints(lessonIndex)" 
                    :key="knowledge.id"
              >
                    <div class="knowledge-name" @click="showKnowledgeDetail(knowledge)">
                  <span class="knowledge-title">{{ knowledge.name }}</span>
                </div>
                <div class="progress-area">
                  <div class="hexagon-group">
                    
                        <!-- 根据levelVos数据动态显示等级（根据训练类型过滤） -->
                        <template v-if="knowledge.levelVos && knowledge.levelVos.length > 0">
                          <div 
                            v-for="(levelItem, levelIndex) in getFilteredLevelVos(knowledge.levelVos)" 
                            :key="levelIndex"
                            class="level-wrapper"
                          >
                            <div class="level-item" style="position: relative;">
                               <img v-if="levelItem.status==1" style="position: absolute;top: -16px;left: 10px;width: 30px;height: 27px;" src="@/assets/img/percision/training/huangg.png" alt="">
                              <img 
                                :src="getLevelImage(levelItem.level)" 
                                :alt="getLevelName(levelItem.level)"
                                class="level-image"
                              />
                              <div class="level-info">
                                <!-- <div class="level-name">{{ getLevelName(levelItem.level) }}</div> -->
                                <div class="level-rate" v-if="levelItem.correctRate">{{ formatCorrectRate(levelItem.correctRate) }}%</div>
                              </div>
                            </div>
                            <el-icon v-if="getFilteredLevelVos(knowledge.levelVos) && levelIndex < getFilteredLevelVos(knowledge.levelVos).length - 1">
                              <CaretRight color="#EAEAEA" />
                            </el-icon>
                          </div>
                        </template>
                        
                        <!-- 兜底显示：如果没有levelVos数据，显示原来的六边形 -->
                        <template v-else>
                    <!-- 基础训练六边形 -->
                     
                    <div class="hexagon-wrapper">
                     
                            <div class="hexagon-bg" :class="getHexagonBgClass(knowledge.correctRate || 0, 'basic')">
                        <div class="hexagon-content">
                          
                                <img v-if="(knowledge.correctRate || 0) >= 100"
                               :src="getSmallMedalIcon(1)"
                               class="medal-crown" />
                                <div class="percentage-text">{{ formatCorrectRate(knowledge.correctRate || 0) }}%</div>
                        </div>
                      </div>
                    </div>

                    <el-icon><CaretRight color="#EAEAEA" /></el-icon>
                    <!-- 进阶训练六边形 -->
                    <div class="hexagon-wrapper">
                            <div class="hexagon-bg" :class="getHexagonBgClass(knowledge.correctRate || 0, 'advanced')">
                        <div class="hexagon-content">
                                <div class="percentage-text">{{ formatCorrectRate(knowledge.correctRate || 0) }}%</div>
                                <img v-if="(knowledge.correctRate || 0) >= 100"
                               :src="getSmallMedalIcon(2)"
                               class="medal-crown" />
                        </div>
                      </div>
                    </div>
                        </template>
                  </div>
                </div>
                <div class="action-area">
                      <div class="action-btn study" @click="toPractice(knowledge)">
                    <img src="@/assets/img/percision/training/play.png" class="action-icon" />
                    去学习
                  </div>
                      <div class="action-btn practice" @click="toRecord(knowledge)">
                    <img src="@/assets/img/percision/training/practice.png" class="action-icon"  />
                    练习记录
                  </div>
                </div>
                
                    <!-- 知识点状态指示器 -->
                    <!-- <div class="knowledge-status" :class="getKnowledgeStatusClass(knowledge)">
                      {{ getKnowledgeStatusText(knowledge) }}
                    </div> -->
              </div>
            </div>

            <!-- 挑战按钮区域 -->
            <div class="challenge-area">
              <!-- 根据状态显示不同内容 -->
              <template v-if="getChallengeButtonStatus({knowledgePoints: getLessonKnowledgePoints(lessonIndex)}).showButton">
                <!-- 显示挑战按钮 -->
                <div 
              class="challenge-btn" 
                  @click="handleChallengeClick({id: lessonIndex + 1, knowledgePoints: getLessonKnowledgePoints(lessonIndex)})"
                >
                  {{ getChallengeButtonText({knowledgePoints: getLessonKnowledgePoints(lessonIndex)}) }}
                </div>
              </template>
              <template v-else>
                <!-- 显示状态图片 -->
                <div class="challenge-status-image">
                  <img 
                    :src="getChallengeButtonStatus({knowledgePoints: getLessonKnowledgePoints(lessonIndex)}).image"
                    :alt="getChallengeButtonStatus({knowledgePoints: getLessonKnowledgePoints(lessonIndex)}).type"
                    class="status-icon"
                    @click="handleStatusImageClick(getChallengeButtonStatus({knowledgePoints: getLessonKnowledgePoints(lessonIndex)}), {id: lessonIndex + 1, knowledgePoints: getLessonKnowledgePoints(lessonIndex)})"
                  />
                </div>
              </template>
            </div> 
              </div>
            </div>
            </div>
          </div>

        <div v-else class="empty-state">
          <img width="112px" height="138px" src="@/assets/img/synchronous/empty.png" alt="">
          <span class="empty-message" style="display: block;">暂无知识点数据</span>
        </div>
      </div>
      <!-- 单元测试 -->
      <div v-else v-loading="loading">
          <div class="test-box">
            <div class="test-wrap" v-for="item in testList">
              <div class="test-box-item">
                <div class="test-box-item-img">
                  <span class="red-text" v-if="item.score&&item.score!='0'">{{item.score}}分</span>
                </div>
                <div class="test-box-item-info">
                  <div class="test-box-item-info-title">
                    {{item.title}}
                  </div>
                  <div class="test-box-item-info-data">
                    <div>更新时间：{{item.reportDate	}}</div>
                    <div>浏览：{{ item.viewCount }}</div>
                  </div>
                </div>
                <div class="test-box-item-btn">
                  <div class="test-box-item-btn-it btn" @click="handleDownload(item)">
                    <img src="@/assets/img/percision/download.png" alt=""> 下载
                  </div>
                  <div class="test-box-item-btn-it blue-text" @click="testDetail(item)">
                    查看详情>
                  </div>
                </div>
              </div>
              <div class="hui-line"></div>
            </div>
          </div>
          <div class="pagination-box">
            <Pagination
                :total="pageData.total"
                :current="pageData.current"
                @currentSizeChange="currentSizeChange"
                @pageClick="pageClick"/>
          </div>
        </div>

      <!-- 知识点详情弹窗 -->
      <el-dialog
        v-model="knowledgeDetailVisible"
        :title="selectedKnowledge?.name || '知识点详情'"
        width="60%"
        :before-close="handleDetailClose"
      >
        <div class="knowledge-detail-content" v-if="selectedKnowledge">
          <div v-html="selectedKnowledge.desc"></div>
      </div>
      </el-dialog>
    </div>
    <div v-if="challengePop" class="elevate-overlay">
      <div class="elevate-ct">
        <div class="close-btn" @click="challengePop = false">
          <img src="@/assets/img/percision/training/hscc.png" alt="">
  </div>
        <!-- {{ currentChallengeInfo.levelName }} -->
        <div class="top-title">本次检测 {{ knowledgeList?.length || 0 }}个知识点</div>
        <div class="block-ct">
          <div 
            class="book-list" 
            v-for="(point, index) in knowledgeList" 
            :key="index"
          >
          <!-- {{ knowledgeList }} -->
            <img src="@/assets/img/percision/training/bookzsd.png" alt="">
            <div class="book-name">{{ index + 1 }}.{{ point.pointName }}</div>
            <div class="book-tl">题量<span class="num">{{ point.quesCount || 3 }}</span></div>
            <div class="book-tl">难度<span class="num">{{ point.degree }}</span></div>
          </div>
          <div class="prompt"> 共 <span>{{ knowledgeOll.quesCount }}</span>道题，要求 <span>{{ knowledgeOll.times }}</span> 分钟内完成</div>
          </div>
        <div class="challenge-fq">向<img :src="currentChallengeInfo.levelImage" :alt="currentChallengeInfo.levelName" class="challenge-level-icon">发起挑战吧，正确率≥90%即可过关！</div>
        <div class="book-challenge" @click="onChallenge">开始挑战</div>
        </div>
      </div>
    </div>
    <!-- 下载试卷 -->
  <downloadTrestDialog v-if="dialogVisible" ref="downloadTrestDialogRef" :paper-detail="dowmloadData" />
</template>

<script lang="ts" setup>
import { useRouter ,useRoute} from 'vue-router'
import knowledgeTree from "@/views/components/knowledgeTree/trainingTree.vue"
import { getBookChapterListApi, getMasteryApi, getBookChapterListsApi, getPointCategoryApi,getChapterListApi,getpointListApi } from "@/api/book"
import {  addTrainingApi, trainingInfoApi} from "@/api/precise"
import { computed, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { getChapterReportListApi } from "@/api/report"
import { useUserStore } from "@/store/modules/user"
import { dataEncrypt, dataDecrypt } from "@/utils/secret"
import { storeToRefs } from 'pinia'
import { CaretRight } from '@element-plus/icons-vue'
const userStore = useUserStore()

const { subjectObj, learnNow } = storeToRefs(userStore)
const learnUsers = JSON.parse(localStorage.learnUsers || "[]") || []
const router = useRouter()
const route = useRoute()
const query = reactive<any>(route.query)
const loading = ref(false)
const loading2 = ref(false)
const pageStatus = ref(true)
const challengePop = ref(false)
// const chapterName = ref("")
const chapterName = ref(initChapterName())

const trainingId = ref()
import downloadTrestDialog from "@/components/TruePaperDownload/index.vue"
const curLevel = ref()
// 存储选中的子级ID数组
const selectedChildIds = ref<string[]>([])

const chapterId = ref("")
const routeHasChapterId = ref(false) // 标记是否从路由获取了章节ID
const isTargetChapterId = ref()
const chapterPath = ref<any>([]) // 存储当前章节路径
const forceUpdatePath = ref(0) // 强制更新路径的计数器
const isDataLoaded = ref(false) // 标记数据是否已经加载过，避免重复调用API

// 本地缓存相关
const CACHE_KEY = 'ai_precision_basic_training_selected_chapter'

// 保存选中章节到本地缓存
const saveSelectedChapterToCache = (chapterData: any) => {
  try {
    // 查找当前章节的完整路径，获取父级信息
    const fullPath:any = findChapterFullPath(options.value, chapterData.id)
    let parentChapterId = null
    let parentChapterName = null
    console.log(fullPath[0],"fullPathfullPathfullPath")
    // 如果找到完整路径且有父级（路径长度大于1）
    if (fullPath ) {
      const parentChapter = fullPath[fullPath.length - 1] // 倒数第二个是父级
      const parentChapters = fullPath[fullPath.length - 2] // 倒数第二个是父级
      console.log(parentChapter,"让我来看看什么万一",parentChapters)
      parentChapterId = parentChapter.id
      parentChapterName = parentChapter.name || parentChapter.chapterName
      
    }
    
    const cacheData = {
      chapterId: chapterData.id,
      chapterName: chapterData.name || chapterData.chapterName,
      parentChapterId: parentChapterId, // 保存父级章节ID
      parentChapterName: parentChapterName, // 保存父级章节名称
      bookId: subjectObj.value.bookId,
      subject: query.subject,
      type: query.type,
      timestamp: Date.now() // 添加时间戳，可用于过期处理
    }
    localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData))
  } catch (error) {
  }
}

// 从本地缓存获取选中章节
const getSelectedChapterFromCache = () => {
  try {
    const cachedData = localStorage.getItem(CACHE_KEY)
    if (!cachedData) {
      return null
    }
    
    const parsedData = JSON.parse(cachedData)
    
    // 检查缓存是否匹配当前的书籍和科目
    if (parsedData.bookId !== subjectObj.value.bookId || 
        parsedData.subject !== query.subject || 
        parsedData.type !== query.type) {
      localStorage.removeItem(CACHE_KEY)
      return null
    }
    
    // 检查缓存是否过期（可选：7天过期）
    const now = Date.now()
    const cacheAge = now - (parsedData.timestamp || 0)
    const maxAge = 7 * 24 * 60 * 60 * 1000 // 7天
    
    if (cacheAge > maxAge) {
      localStorage.removeItem(CACHE_KEY)
      return null
    }
    
    return parsedData
  } catch (error) {
    localStorage.removeItem(CACHE_KEY)
    return null
  }
}

// 清除章节缓存
const clearSelectedChapterCache = () => {
  try {
    localStorage.removeItem(CACHE_KEY)
  } catch (error) {
  }
}

// 从缓存中获取父级章节信息
const getParentChapterFromCache = () => {
  const cachedData = getSelectedChapterFromCache()
  if (cachedData && cachedData.parentChapterId && cachedData.parentChapterName) {
    return {
      parentChapterId: cachedData.parentChapterId,
      parentChapterName: cachedData.parentChapterName
    }
  }
  return null
}

// 获取当前章节的父级信息
const getCurrentChapterParent = (chapterId: string) => {
  const fullPath = findChapterFullPath(options.value, chapterId)
  if (fullPath && fullPath.length > 1) {
    const parentChapter = fullPath[fullPath.length - 2]
    return {
      parentChapterId: parentChapter.id,
      parentChapterName: parentChapter.name || parentChapter.chapterName
    }
  }
  return null
}

const knowledgeList = ref<any>([])
const knowledgeOll = reactive({
  times: 120, // 时间，单位：分钟（从API返回的秒数转换而来）
  quesCount: 1 // 总题目数量
})

// 监听chapterId变化，用于调试
watch(() => chapterId.value, (newValue, oldValue) => {
  if (newValue) {
    // 强制刷新路径计算
    forceUpdatePath.value++
  }
}, { immediate: true })

// 监听chapterPath变化
watch(() => chapterPath.value, (newPath, oldPath) => {
  // 强制刷新路径计算
  forceUpdatePath.value++
}, { immediate: true, deep: true })

const testList = ref([] as any[])
// 计算属性：格式化章节路径文本 - 优化版
const chapterPathText = computed(() => {
  // 如果没有选中章节ID，返回默认文本
  if (!chapterId.value) {
    return "请选择章节"
  }
  
  // 如果没有章节数据，返回默认文本
  if (!options.value || options.value.length === 0) {
    return "请选择章节"
  }
  
  // 查找完整路径
  const fullPath = findChapterFullPath(options.value, chapterId.value)
  
  if (fullPath && fullPath.length > 0) {
    const pathText = buildSimplePathText(fullPath)
    
    // 更新全局路径变量
    chapterPath.value = fullPath
    
    return pathText
  }
  
  return "未找到章节"
})

// 查找章节完整路径 - 新的简化版本
const findChapterFullPath = (chapters: any[], targetId: string): any[] | null => {
  if (!chapters || chapters.length === 0) {
    return null
  }
  
  const searchPath = (nodes: any[], currentPath: any[] = [], depth: number = 0): any[] | null => {
    for (let i = 0; i < nodes.length; i++) {
      const chapter = nodes[i]
      const newPath = [...currentPath, chapter]
      
      // 类型转换比较 - 确保类型一致
      const chapterIdStr = String(chapter.id)
      const targetIdStr = String(targetId)
      
      // 额外的数字比较（处理大数字精度问题）
      const chapterIdNum = Number(chapter.id)
      const targetIdNum = Number(targetId)
      
      const isMatch = chapterIdStr === targetIdStr || chapterIdNum === targetIdNum
      
      // 如果找到目标章节
      if (isMatch) {
        return newPath
      }
      
      // 如果有子章节，递归搜索
      if (chapter.children && Array.isArray(chapter.children) && chapter.children.length > 0) {
        const result = searchPath(chapter.children, newPath, depth + 1)
        if (result) {
          return result
        }
      }
    }
    
    return null
  }
  
  const result = searchPath(chapters)
  return result
}

// 构建简单路径文本
const buildSimplePathText = (pathArray: any[]): string => {
  if (!pathArray || pathArray.length === 0) {
    return ""
  }
  
  const pathNames = pathArray
    .map((chapter, index) => {
      // 获取章节名称，优先级：chapterName > name > title
      const name = chapter.chapterName || chapter.name || chapter.title || `章节${index + 1}`
      return name.trim()
    })
    .filter(name => {
      const isValid = name && name.length > 0
      return isValid
    })
  
  const result = pathNames.join(' > ')
  return result
}

// 调试章节数据结构的函数
const debugChapterStructure = (chapters: any[], maxDepth: number = 3, currentDepth: number = 0) => {
  if (!chapters || chapters.length === 0 || currentDepth >= maxDepth) {
    return
  }
  
  chapters.forEach((chapter, index) => {
    if (chapter.children && chapter.children.length > 0 && currentDepth < maxDepth - 1) {
      debugChapterStructure(chapter.children, maxDepth, currentDepth + 1)
    }
  })
}

// 构建路径文本的辅助函数 - 优化版
const buildPathText = (pathArray) => {
  if (!pathArray || pathArray.length === 0) {
    return ""
  }
  
  // 处理路径数组，确保获取正确的章节名称
  const pathNames = pathArray
    .map((chapter, index) => {
      // 多重字段检查，确保获取到正确的名称
      let name = ''
      
      // 按优先级获取名称
      if (chapter.chapterName && chapter.chapterName.trim()) {
        name = chapter.chapterName.trim()
      } else if (chapter.name && chapter.name.trim()) {
        name = chapter.name.trim()
      } else if (chapter.title && chapter.title.trim()) {
        name = chapter.title.trim()
      } else {
        name = `未命名章节${index + 1}`
      }
      
      
      return name
    })
    .filter(name => {
      // 过滤掉无效名称
      const isValid = name && 
                      name !== '' && 
                      name !== '未命名章节' && 
                      !name.startsWith('未命名章节') && 
                      name.trim().length > 0
      
      return isValid
    })
  
  // 构建最终路径文本
  const result = pathNames.length > 0 ? pathNames.join(' > ') : ""
  
  return result
}

// 查找目标章节的辅助函数
const findTargetChapter = (chapters, targetId) => {
  
  const search = (nodes, path = []) => {
    for (const chapter of nodes) {
      const currentPath:any = [...path, chapter]
      if (chapter.id === targetId) {
        return { chapter, fullPath: currentPath }
      }
      
      if (chapter.children && chapter.children.length > 0) {
        const result = search(chapter.children, currentPath)
        if (result) return result
      }
    }
    return null
  }
  
  const result = search(chapters)
  return result ? result.chapter : null
}


// 新增：直接获取完整路径的函数
const getFullChapterPath = (chapters, targetId) => {
  
  // 首先调试章节结构
  debugChapterStructure(chapters, 2)
  
  const search = (nodes, path = []) => {
    for (const chapter of nodes) {
      const currentPath:any = [...path, chapter]
      
      if (chapter.id === targetId) {
        return currentPath
      }
      
      if (chapter.children && chapter.children.length > 0) {
        const result = search(chapter.children, currentPath)
        if (result) return result
      }
    }
    return null
  }
  
  const result = search(chapters)
  return result
}

const chapterData = reactive<any>({
  percentage1: null,
  percentage1i: null,
  strong1: false,
  rate1: 1,
  percentage2: null,
  percentage2i: null,
  strong2: false,
  rate2: 2,
  percentage3: null,
  percentage3i: null,
  strong3: false,
  rate3: 3,
  percentage0: null,
  percentage0i: null,
  strong0: false,
})
const options = ref([])

// 定义等级数据接口
interface LevelVo {
  level: number | null
  correctRate: number
  status: number // 状态字段：0-未开始，1-已完成，2-挑战过但未通过
}

// 定义知识点接口
interface KnowledgePoint {
  id: string
  name: string
  correctRate?: number
  studyStatus?: number
  status?: number
  difficulty?: number
  levelVos?: LevelVo[]
  basicProgress?: number
  advancedProgress?: number
  isCompleted?: boolean
  completionLevel?: string | null
  total?: number
  desc?: string // 知识点描述
  digestiveMarkers?: any[] // 错题记录
  [key: string]: any // 允许其他动态属性
}

// 课程数据结构 - 直接使用知识点数组
const lessonData = ref<KnowledgePoint[]>([
      {
        id: 'k1',
    name: '知识点名称1',
    correctRate: 89.4,
    studyStatus: 0,
    status: 5,
    levelVos: [
      { level: 1, correctRate: 89.4, status: 1 }, // 青铜已完成，可以挑战白银
      { level: 2, correctRate: 0, status: 0 }
    ]
      },
      {
        id: 'k2', 
    name: '知识点名称2',
    correctRate: 100,
    studyStatus: 1,
    status: 1,
    levelVos: [
      { level: 1, correctRate: 100, status: 1 }, // 青铜已完成，可以挑战白银
      { level: 2, correctRate: 95, status: 0 }
    ]
  },
      {
        id: 'k3',
    name: '知识点名称3',
    correctRate: 60,
    studyStatus: 0,
    status: 2,
    levelVos: [
      { level: 1, correctRate: 60, status: 2 }, // 青铜挑战过但未通过，需要再次挑战
      { level: 2, correctRate: 0, status: 0 }
    ]
      },
      {
        id: 'k4',
    name: '知识点名称4',
    correctRate: 65,
    studyStatus: 0,
    status: 2,
    levelVos: [
      { level: 1, correctRate: 65, status: 2 }, // 青铜挑战过但未通过，需要再次挑战
      { level: 2, correctRate: 0, status: 0 }
    ]
  },
      {
        id: 'k5',
    name: '知识点名称5',
    correctRate: 80,
    studyStatus: 0,
    status: 2,
    levelVos: [
      { level: 2, correctRate: 80, status: 2 }, // 白银挑战过但未通过，需要再次挑战
      { level: 3, correctRate: 0, status: 0 }
    ]
      },
      {
        id: 'k6',
    name: '知识点名称6',
    correctRate: 85,
    studyStatus: 0,
    status: 2,
    levelVos: [
      { level: 2, correctRate: 85, status: 2 }, // 白银挑战过但未通过，需要再次挑战
      { level: 3, correctRate: 0, status: 0 }
    ]
  },
  {
    id: 'k7',
    name: '知识点名称7（含null等级）',
    correctRate: 0,
    studyStatus: 0,
    status: 5,
    levelVos: [
      { level: null, correctRate: 0, status: 0 }, // null等级，应该显示灰色图片
      { level: 1, correctRate: 0, status: 0 }
    ]
  },
  {
    id: 'k8',
    name: '知识点名称8（测试格式化）',
    correctRate: 95.00, // 测试.00格式
    studyStatus: 1,
    status: 1,
    levelVos: [
      { level: 1, correctRate: 95.00, status: 1 }, // 测试95.00显示为95
      { level: 2, correctRate: 87.50, status: 0 }  // 测试87.50正常显示
    ]
  }
])

// 处理挑战按钮点击
const handleChallengeClick = (lesson: { id: number; knowledgePoints: KnowledgePoint[] }) => {
  
  // 只获取当前课程的知识点ID列表，不累积之前的
  const currentPointIds = lesson.knowledgePoints
    .filter((point: KnowledgePoint) => point.id) // 确保ID存在
    .map((point: KnowledgePoint) => point.id);
  
  // 根据当前课程知识点状态计算挑战等级
  const challengeLevel = getChallengeLevel(lesson);
  const challengeLevelName = getLevelName(challengeLevel);
  
  // 获取挑战按钮文本用于调试验证
  const challengeButtonText = getChallengeButtonText(lesson);
  
  // 清空之前的选中ID，只使用当前课程的ID
  selectedChildIds.value = [...currentPointIds];
  curLevel.value = challengeLevel.toString() 
  addTrainingApi({
    bookId: query.bookId,
    pointIds: currentPointIds, // 直接使用当前课程的知识点ID
    subject:query.subject,
    chapterId:chapterId.value,
    level: challengeLevel.toString() // 动态传递挑战等级：1青铜 2白银 3黄金 4钻石

  }).then((res:any) => {
    if (res.code === 200) {
      if(res.data){
        trainingId.value = res.data
          trainingInfoApi({
           trainingId: res.data
         }).then((res: any) => {
           if (res.code == 200) {
             
             // 处理时间转换：将秒转换为分钟
             const timeInSeconds = res.data.times || 0;
             const timeInMinutes = convertSecondsToMinutes(timeInSeconds);
             
             // 显示挑战弹窗
             knowledgeList.value = res.data.pointItems
             challengePop.value = true;
             knowledgeOll.times = timeInMinutes; // 存储转换后的分钟数
             knowledgeOll.quesCount = res?.data?.quesCount;
           }

       })
    } 
  }
  })

  // 设置弹窗数据
  selectedLessonForChallenge.value = {
    id: lesson.id,
    knowledgePoints: lesson.knowledgePoints,
    pointIds: currentPointIds, // 使用当前课程的知识点ID
    currentChildIds: currentPointIds // 当前选中的子级ID就是当前课程的ID
  };
}

// 处理状态图片点击事件
const handleStatusImageClick = (buttonStatus: any, lesson: { id: number; knowledgePoints: KnowledgePoint[] }) => {
  
  if (buttonStatus.type === 'wrong_questions') {
    // 点击错题复习图片，跳转到错题复习页面
    
    // 收集所有知识点的错题信息
    const wrongQuestions: any[] = []
    lesson.knowledgePoints.forEach(knowledge => {
      if (knowledge.digestiveMarkers && knowledge.digestiveMarkers.length > 0) {
        wrongQuestions.push(...knowledge.digestiveMarkers)
      }
    })
    
    
    // 这里可以跳转到错题复习页面
    // router.push({
    //   path: '/ai_percision/wrong_questions_review',
    //   query: {
    //     data: dataEncrypt({
    //       lessonId: lesson.id,
    //       wrongQuestions: wrongQuestions,
    //       chapterId: chapterId.value
    //     })
    //   }
    // })
    
    
  } else if (buttonStatus.type === 'perfect') {

  }
}

// 存储当前选中的课程数据，用于挑战
const selectedLessonForChallenge = ref<any>(null);
  const goBack = () =>{
    router.go(-1)
  }


// 将秒转换为分钟的辅助函数
const convertSecondsToMinutes = (seconds: number): number => {
  if (!seconds || seconds <= 0) {
    return 2; // 默认2分钟
  }
  
  const minutes = Math.ceil(seconds / 60); // 向上取整
  
  // 确保最少1分钟
  return Math.max(minutes, 1);
};


// 开始挑战
const onChallenge = () => {

  // 显示挑战等级信息
  if (selectedLessonForChallenge.value) {
    const challengeLevel = getChallengeLevel(selectedLessonForChallenge.value);
    const challengeLevelName = getLevelName(challengeLevel);
  }
  
    router.push({
      path: '/ai_percision/answer_training',
      // path: '/ai_percision/entrance_assessment/doing_exercises',
      query: {
        data: dataEncrypt({
          reportId: trainingId.value ,
          pageSource: '1',
          bookId: subjectObj.value.bookId,
          chapterId:chapterId.value,
          level:curLevel.value
        }),
      }
    })
}

const getUrl = (item: any) => {
    let url = 'pen'
    if (item > 90) {
        url = 'strong'
    }
    return new URL(`../../../assets/img/percision/${url}.png`, import.meta.url).href //静态资源引入为url，相当于require()
}
// 递归查找章节，并返回包含该章节的完整路径
const findChapterById = (chapters:any, targetId:any, path:any = []) => {
  let deepestPath: any[] | null = null;

  for (const chapter of chapters) {
    // 确保章节有chapterName属性
    if (!chapter.chapterName && chapter.name) {
      chapter.chapterName = chapter.name
    }
    
    // 创建当前路径的副本
    const currentPath:any = [...path, chapter]

    // 如果找到目标章节，记录当前路径
    if (chapter.id === targetId) {
      // 记录找到的路径，但不立即返回
      deepestPath = currentPath;
      // 更新全局的章节路径
      chapterPath.value = deepestPath;
    }

    // 无论是否已找到匹配节点，都继续递归查找子节点
    if (chapter.children && chapter.children.length > 0) {
      const childResult: any[] | null = findChapterById(chapter.children, targetId, currentPath)
      // 如果子节点中找到了结果，优先使用子节点的结果（更深层次）
      if (childResult) {
        // 如果已经有记录但找到了更深的路径，或者还没有记录
        if (!deepestPath || childResult.length > deepestPath.length) {
          deepestPath = childResult;
          // 更新全局的章节路径
          chapterPath.value = deepestPath;
        }
      }
    }
  }

  return deepestPath;
}

onMounted(async () => {
  
  // 从路由参数中获取章节ID
  let targetChapterId = null

  if (query.chapterId) {
    targetChapterId = query.chapterId
  } else if (query.data) {
    try {
      const decryptedData = dataDecrypt(query.data)
      if (decryptedData && decryptedData.chapterId) {
        targetChapterId = decryptedData.chapterId
      }
    } catch (error) {
      console.error('Failed to decrypt query data:', error)
    }
  }

  // 获取章节列表
  await getBookChapterList()
  
  // 优先级处理：路由参数 > 本地缓存 > 默认选择
  if (targetChapterId) {
    chapterId.value = targetChapterId
    routeHasChapterId.value = true

    // 使用新的路径查找函数
    const foundPath = findChapterFullPath(options.value, targetChapterId)
    if (foundPath && foundPath.length > 0) {
      const targetChapter = foundPath[foundPath.length - 1]
      pageStatus.value = targetChapter.name !== "单元测试"
      chapterName.value = targetChapter.name || targetChapter.chapterName
      
      // 更新全局路径变量
      chapterPath.value = foundPath
      
      // 保存到缓存
      saveSelectedChapterToCache(targetChapter)
    }

    if(!pageStatus.value) {
      getChapterReportList()
    } else {
    getMastery()
  }
  } else {
    // 尝试从缓存恢复选中状态
    const cachedChapter = getSelectedChapterFromCache()
    if (cachedChapter && cachedChapter.chapterId) {
      
      // 验证缓存的章节ID是否在当前章节列表中存在
      const foundPath = findChapterFullPath(options.value, cachedChapter.chapterId)
      if (foundPath && foundPath.length > 0) {
        const targetChapter = foundPath[foundPath.length - 1]
        chapterId.value = cachedChapter.chapterId
        chapterName.value = targetChapter.name || targetChapter.chapterName
        pageStatus.value = (targetChapter.name || targetChapter.chapterName) !== "单元测试"
        chapterPath.value = foundPath
              
        // 如果有父级信息，也输出显示
        if (cachedChapter.parentChapterId && cachedChapter.parentChapterName) {
          console.log("✅ 恢复的父级章节:", cachedChapter.parentChapterName, "(ID:", cachedChapter.parentChapterId, ")")
        }
        
        // 立即加载数据
        if(!pageStatus.value) {
          getChapterReportList()
        } else {
          getMastery()
        }
      } else {
        clearSelectedChapterCache()
        handleDefaultSelectionWithCache()
      }
    } else {

      handleDefaultSelectionWithCache()
    }
  }
  
  // 延迟打印课程数据，确保数据已经加载完成
  setTimeout(() => {
    printLessonData()
  }, 1000)
})

// 根据模式获取存储的选中信息（包含id和name）
const getStoredSelectedInfo = () => {
  const storageKey = `selectedChapterInfo_${ 'synchronous'}`
  const stored = localStorage.getItem(storageKey)
  try {
    return stored ? JSON.parse(stored) : null
  } catch (error) {
    console.error('解析存储的选中信息失败:', error)
    return null
  }
}
// 初始化章节名称，优先使用存储的名称
const initChapterName = () => {
  const storedInfo = getStoredSelectedInfo()
  console.log(storedInfo,"storedInfostoredInfostoredInfo")
  if (storedInfo && storedInfo.name) {
    return storedInfo.name
  }
  return userStore.chapterObj.chapterName
}


// 打印选中左侧知识点下每一个课的数据
const printLessonData = () => {
  
  if (!chapterId.value) {
  } else if (!options.value || options.value.length === 0) {
  } else {
    debugChapterStructure(options.value, 2)
    
    const foundPath = findChapterFullPath(options.value, chapterId.value)
 
    if (foundPath && foundPath.length > 0) {
      const pathText = buildSimplePathText(foundPath)
    } else {
    }
  }
  
  // 然后获取计算属性的值
  const pathResult = chapterPathText.value
  
  if (lessonData.value.length === 0) {
    return
  }
  
  // 按照动态知识点数量分为一课的逻辑来打印
  const totalLessonsCount = totalLessons.value
  const pointsPerLessonCount = pointsPerLesson.value
  
  for (let lessonIndex = 0; lessonIndex < totalLessonsCount; lessonIndex++) {
    const lessonNumber = lessonIndex + 1
    const lessonKnowledgePoints = getLessonKnowledgePoints(lessonIndex)
    const startIndex = lessonIndex * pointsPerLessonCount
    const endIndex = Math.min(startIndex + pointsPerLessonCount, lessonData.value.length)
    
    // 打印每个知识点的详细信息
    lessonKnowledgePoints.forEach((knowledge: KnowledgePoint, index: number) => {
    
      // 详细打印levelVos数组
      if (knowledge.levelVos && Array.isArray(knowledge.levelVos)) {
        knowledge.levelVos.forEach((levelItem: LevelVo, levelIndex: number) => {
        })
      } else {
        console.log(`    ⚠️ 该知识点没有levelVos数据`)
      }
    })
    
    // 打印挑战按钮相关信息
    const lessonForChallenge = {
      id: lessonNumber,
      knowledgePoints: lessonKnowledgePoints
    }
    
    const challengeButtonText = getChallengeButtonText(lessonForChallenge)
    const challengeLevel = getChallengeLevel(lessonForChallenge)
    const challengeLevelName = getLevelName(challengeLevel)
    
    const buttonStatus = getChallengeButtonStatus(lessonForChallenge)
    
    let allStatus1 = true // 所有第一条数据status都等于1
    let allStatus2WithRate = true // 所有第一条数据status都等于2且correctRate>0
    
    lessonKnowledgePoints.forEach((knowledge: KnowledgePoint, index: number) => {
      if (knowledge.levelVos && knowledge.levelVos.length > 0) {
        const firstLevel = knowledge.levelVos[0]
        if (firstLevel) {
          const statusText = firstLevel.status === 1 ? '✅已完成' : 
                            firstLevel.status === 2 ? '🔄挑战过' : '❌未完成'
                   
          // 检查状态
          if (firstLevel.status !== 1) allStatus1 = false
          if (firstLevel.status !== 2 || (firstLevel.correctRate || 0) <= 0) allStatus2WithRate = false
        } else {
          allStatus1 = false
          allStatus2WithRate = false

        }
      } else {
        allStatus1 = false
        allStatus2WithRate = false

      }
    })
    
    
    // 根据状态分析预期的按钮文本（考虑训练类型限制）
    const { min, max } = getAllowedLevelRange()
    let expectedButtonText = ""
    let expectedLevel = min
    
    const firstKnowledge = lessonKnowledgePoints.length > 0 ? lessonKnowledgePoints[0] : null
    
    if (allStatus1 && firstKnowledge?.levelVos && firstKnowledge.levelVos.length > 0) {
      const firstLevelVo = firstKnowledge.levelVos[0]
      if (firstLevelVo && firstLevelVo.level !== null) {
        const firstLevelValue = firstLevelVo.level
        const nextLevel = Math.min(firstLevelValue + 1, max)
        const nextLevelName = getLevelName(nextLevel)
        expectedButtonText = `挑战${nextLevelName}`
        expectedLevel = nextLevel
      } else {
        expectedButtonText = `挑战${getLevelName(min)}`
        expectedLevel = min
      }
    } else if (allStatus2WithRate && firstKnowledge?.levelVos && firstKnowledge.levelVos.length > 0) {
      const firstLevelVo = firstKnowledge.levelVos[0]
      if (firstLevelVo && firstLevelVo.level !== null) {
        const challengeLevel = Math.max(min, Math.min(firstLevelVo.level, max))
        const currentLevelName = getLevelName(challengeLevel)
        expectedButtonText = `再次挑战${currentLevelName}`
        expectedLevel = challengeLevel
      } else {
        expectedButtonText = `挑战${getLevelName(min)}`
        expectedLevel = min
      }
    } else if (firstKnowledge?.levelVos && firstKnowledge.levelVos.length > 0) {
      const firstLevelVo = firstKnowledge.levelVos[0]
      if (firstLevelVo && firstLevelVo.level !== null) {
        const challengeLevel = Math.max(min, Math.min(firstLevelVo.level, max))
        const currentLevelName = getLevelName(challengeLevel)
        expectedButtonText = `挑战${currentLevelName}`
        expectedLevel = challengeLevel
      } else {
        expectedButtonText = `挑战${getLevelName(min)}`
        expectedLevel = min
        console.log(`    📝 预期挑战按钮文本: "挑战${getLevelName(min)}" (level: ${min}, firstLevelVo无效)`)
      }
    } else {
      expectedButtonText = `挑战${getLevelName(min)}`
      expectedLevel = min
      console.log(`    📝 预期挑战按钮文本: "挑战${getLevelName(min)}" (level: ${min}, 训练类型${query.type})`)
    }
    
    // 验证实际结果与预期是否一致
    const isTextMatched = challengeButtonText === expectedButtonText
    const isLevelMatched = challengeLevel === expectedLevel
  }
}

// 查找第一个叶子节点（参考knowledge_graph_detail页面实现）
const findFirstLeafChapter = (chapters) => {
  if (!chapters || chapters.length === 0) return null
  
  for (const chapter of chapters) {
    if (chapter.children && chapter.children.length > 0) {
      const leafChapter = findFirstLeafChapter(chapter.children)
      if (leafChapter) return leafChapter
    } else {
      return chapter
    }
  }
  return null
}


// 处理默认选中逻辑（参考knowledge_graph_detail页面实现）
const handleDefaultSelection = () => {

  if (options.value.length > 0) {
    // 没有存储信息，使用options第一条数据作为默认值
    selectFirstOption()
  } else {
    console.log("❌ 没有章节数据，无法选择")
  }
}

// 带缓存的默认选中逻辑
const handleDefaultSelectionWithCache = () => {

  if (options.value.length > 0) {
    // 使用第一个选项并保存到缓存
    selectFirstOptionWithCache()
  } else {
    console.log("❌ 没有章节数据，无法选择")
  }
}

// 选择第一个可用选项（参考knowledge_graph_detail页面实现）
const selectFirstOption = () => {
  const firstOption = findFirstLeafChapter(options.value)
  if (firstOption) {
    const defaultId = firstOption.id
    const defaultName = firstOption.name || firstOption.chapterName
   
    // 先设置值
    chapterId.value = defaultId
    chapterName.value = defaultName
    
    // 设置页面状态
    pageStatus.value = defaultName !== "单元测试"
    
    // 设置章节路径 - 使用新的路径查找函数
    const foundPath = findChapterFullPath(options.value, defaultId) || [firstOption]
    chapterPath.value = foundPath
    
 
    // 使用nextTick确保DOM更新后再调用
    nextTick(() => {
      setChapterId(firstOption, defaultName)
      // 强制更新路径计算
      forceUpdatePath.value++
    })
  } else {
    console.log("❌ selectFirstOption 未找到可用的叶子节点")
  }
}

// 带缓存的选择第一个可用选项
const selectFirstOptionWithCache = () => {
  const firstOption = findFirstLeafChapter(options.value)
  if (firstOption) {
    const defaultId = firstOption.id
    const defaultName = firstOption.name || firstOption.chapterName
    
    // 先设置值
    chapterId.value = defaultId
    chapterName.value = defaultName
    
    // 设置页面状态
    pageStatus.value = defaultName !== "单元测试"
    
    // 设置章节路径 - 使用新的路径查找函数
    const foundPath = findChapterFullPath(options.value, defaultId) || [firstOption]
    chapterPath.value = foundPath
    
    
    // 保存到缓存
    saveSelectedChapterToCache(firstOption)
    
    
    // 不调用setChapterId，避免重复调用getMastery
    // 因为onMounted中已经会调用一次getMastery
  } else {
    console.log("❌ selectFirstOptionWithCache 未找到可用的叶子节点")
  }
}
const pageData = reactive({
  total: 0,
  current: 1,
  size: 10
})

const pageClick = (val: number) => {
  pageData.current = val
  getChapterReportList()
}
// 单元测试
const dialogVisible = ref(false)
const currentSizeChange = (currentPage: number, pageSize: number) => {
  pageData.current = currentPage
  pageData.size = pageSize
  // getChapterReportList()
}
//下载试卷
const downloadTrestDialogRef = ref()
const dowmloadData = reactive({
    id: '',
    title: ''
})
const handleDownload = ({ id, title }: any) => {
  Object.assign(dowmloadData, {
    id: id,
    title: title
  })
  dialogVisible.value = true
  nextTick(() => {
    downloadTrestDialogRef.value.dialogShow()
  })
}
const testDetail = (data: any) => {
  router.push({
    path: '/ai_percision/knowledge_graph_detail_unit/paper_detailU',
    query: {
      data: dataEncrypt({
        reportId: data.id,
        pageSource: '3'
      }),
    }
  })
}

//获取试卷列表
const getChapterReportList = async() => {
  loading2.value = true
  try {
    const res: any = await getChapterReportListApi({
      chapterId: chapterId.value,
      bookId: subjectObj.value.bookId,
      isSmall: 1,
      type: 1,
      size: pageData.size,
      current: pageData.current
    })
    if(res.code == 200) {
      testList.value = res.data.records || []
      pageData.total = Number(res.data.total)
    }
    loading2.value = false
  } catch (error) {
    testList.value = []
    console.log(error)
    loading2.value = false
  }
}

//获取章节列表
const getBookChapterList = async() => {
  if(query.contentType == 'historyTask'){
    subjectObj.value.bookId = query.bookId
  }
  loading.value = true
  loading2.value = true
  try {
    const res: any = await getChapterListApi({
      bookId:subjectObj.value.bookId,
      hierarchy: 3,
      type: query.type,
      chapterIds: query?.resourceIds?query?.chapterId:''
    })
    if(res.code == 200) {
      
      // 确保所有节点都有chapterName属性
      const processChapterData = (chapters) => {
        return chapters.map(chapter => {
          // 确保每个节点都有chapterName属性
          const processedChapter = {
            ...chapter,
            chapterName: chapter.chapterName || chapter.name || chapter.title || '未命名章节'
          }
          
          // 递归处理子节点
          if (processedChapter.children && processedChapter.children.length > 0) {
            processedChapter.children = processChapterData(processedChapter.children)
          }
          
          return processedChapter
        })
      }
      
      // 处理API返回的数据，确保所有节点都有chapterName
      const processedData = processChapterData(res.data || [])
      options.value = processedData
          
      // 处理默认选中逻辑
      handleDefaultSelection()
    }
    loading.value = false
    return res.data || []
  } catch (error) {
    options.value = []
    console.log(error)
    loading.value = false
    return []
  }
}
const getLast = (data: any) => {
  let id = ""
  if(data.children && data.children.length > 0) {
    // 构建路径
    const path = [data]
    const firstChild = data.children[0]
    if (firstChild) {
      const lastId = getLast(firstChild)
      
      // 如果是第一次调用（没有路径设置），则设置路径
      if (chapterPath.value.length === 0) {
        // 查找完整路径并设置
        findChapterById(options.value, lastId)
      }
      
      return lastId
    } else {
      // 如果children为空数组，返回当前节点id
      return data.id || ""
    }
  } else {
    id = data.id
    
    // 如果是第一次调用（没有路径设置），则设置路径
    if (chapterPath.value.length === 0) {
      chapterPath.value = [data]
    }
    
    return id
  }
}
// 左侧选中Id
const setChapterId = (data: any, name: string, saveCache: boolean = true) => {
  
  // 使用chapterName作为节点名称，如果不存在则回退到name
  const nodeName = data.chapterName || data.name
  pageStatus.value = nodeName != "单元测试"
  
  // 根据iswen属性决定使用哪个ID字段
  const nodeId = data.id || data.chapterId
  chapterId.value = nodeId
  
  // 设置章节名称
  chapterName.value = name || nodeName
  

  // 当用户手动选择章节时，重置标记
  routeHasChapterId.value = false

  // 重置数据加载标记，允许重新加载数据
  isDataLoaded.value = false
  
  // 使用新的路径查找函数
  const foundPath = findChapterFullPath(options.value, nodeId)
  
  if (foundPath && foundPath.length > 0) {
    chapterPath.value = foundPath

    foundPath.forEach((node, index) => {
      console.log(`  ${index + 1}. ${node.name || node.chapterName || node.title} (ID: ${node.id})`)
    })
  } else {
    // 备用方案，至少设置当前节点
    chapterPath.value = [data]

  }
  
  // 保存到缓存（如果需要）
  if (saveCache) {
    saveSelectedChapterToCache(data)
  }
  
  // 强制触发路径文本更新
  forceUpdatePath.value++
  
    // 根据页面状态决定加载什么数据
    if(!pageStatus.value) {

      getChapterReportList()
    } else {

      getMastery(true) // 强制重新加载
    }
  // 可选：更新URL，保持状态同步（不刷新页面）
  router.replace({
    query: {
      ...route.query,
      chapterId: data.id
    }
  })
}

// 知识点列表
const getMastery = async(forceReload: boolean = false) => {  
  // 如果数据已经加载过且不是强制重新加载，则跳过
  if (isDataLoaded.value && !forceReload) {
    console.log("📊 数据已加载，跳过重复调用")
    return
  }
  
  loading2.value = true
  try {
    const res: any = await getpointListApi({
      chapterId: chapterId.value,
      type:query.type
    })
    
    console.log("📊 知识点API响应:", res)

    if (res.code === 200 && res.data && Array.isArray(res.data)) {
      // 将API返回的知识点数据直接使用，保留原始属性
      lessonData.value = res.data.map((point: any): KnowledgePoint => ({
        ...point,
        // 确保关键属性存在
        id: point.id || '',
        name: point.name || '',
        correctRate: point.correctRate || 0,
        studyStatus: point.studyStatus || 0,
        status: point.status || 5, // 默认为未测试
        levelVos: point.levelVos ? point.levelVos.map((levelItem: any): LevelVo => ({
          level: levelItem.level,
          correctRate: levelItem.correctRate || 0,
          status: levelItem.status || 0 // 确保status字段存在
        })) : [], // 确保levelVos存在并且每项都有status字段
      }));
      

      // 如果API返回了掌握度数据，则更新chapterData
      if (res.data[0] && res.data[0].trainCollect) {
      res.data[0].trainCollect.map((item:any) => {
        let num = Number(item.mastery)
        if (item.type == 1) {
          chapterData.percentage1 = num
          chapterData.percentage1i = parseFloat((num).toFixed(2))
        } else if (item.type == 2) {
          chapterData.percentage2 = num
          chapterData.percentage2i = parseFloat((num).toFixed(2))
        } else if (item.type == 3) {
          chapterData.percentage3 = num
          chapterData.percentage3i = parseFloat((num).toFixed(2))
        } else if (item.type == 0) {
          chapterData.percentage0 = num
          chapterData.percentage0i = parseFloat((num).toFixed(2))
        }
      })
    } else {
        // 如果没有掌握度数据，则根据知识点状态计算掌握度
        const totalPoints = lessonData.value.length;
        if (totalPoints > 0) {
          const masteredPoints = lessonData.value.filter(point => point.status === 1).length;
          const masteryPercentage = (masteredPoints / totalPoints) * 100;
          
          chapterData.percentage1 = masteryPercentage;
          chapterData.percentage1i = parseFloat(masteryPercentage.toFixed(2));
        } else {
          resetChapterData();
        }
      }
    } else {
      // 如果API没有返回有效数据，则重置
      lessonData.value = [];
      resetChapterData();
    }
    loading2.value = false;
    
    // 标记数据已加载
    isDataLoaded.value = true;
    
    // 数据加载完成后打印课程数据
    setTimeout(() => {
      printLessonData()
    }, 100)
    
  } catch (error) {
    console.error('获取知识点数据失败:', error);
    lessonData.value = [];
    resetChapterData();
    loading2.value = false;
  }
}

// 重置章节数据
const resetChapterData = () => {
  chapterData.percentage1 = 0;
  chapterData.percentage1i = 0;
  chapterData.percentage2 = 0;
  chapterData.percentage2i = 0;
  chapterData.percentage3 = 0;
  chapterData.percentage3i = 0;
  chapterData.percentage0 = 0;
  chapterData.percentage0i = 0;
}

// 去学习
const toPractice = (val: KnowledgePoint) => {
  console.log(val,"1111111111111111")
  router.push({
        name: 'TeachRoomTeachVideo',
    query: {
          id: val.id,
          pointName: val.name,
          source: 'analysis',
          subject: query.subject,
    }
  })
}
// 查看记录
const toRecord = (val: KnowledgePoint) => {
  console.log(val,"1111111111111111")
  router.push({
      path: '/ai_percision/foundation_report',
    query: {
      data: dataEncrypt({
            // reportId: detailData.trainingId,
            id: val.id,
            source: 'record',
      })
    }
  })
}

const bookVersionName = computed(() => {
  return subjectObj.value.editionName + learnNow.value.gradeName + (subjectObj.value.termName?subjectObj.value.termName:"")
})

// 根据年级计算每课显示的知识点数量
const pointsPerLesson = computed(() => {
  // 如果当前年级大于6，每课显示3个知识点，否则显示2个
  const gradeId = learnUsers[0]?.gradeId || 0
  return gradeId > 6 ? 3 : 2
})

// 根据知识点数量计算总课程数
const totalLessons = computed(() => {
  return Math.ceil(lessonData.value.length / pointsPerLesson.value)
})

// 获取指定课程的知识点
const getLessonKnowledgePoints = (lessonIndex: number): KnowledgePoint[] => {
  if (!lessonData.value || lessonData.value.length === 0) {
    return []
  }
  
  const startIndex = lessonIndex * pointsPerLesson.value
  const endIndex = startIndex + pointsPerLesson.value
  
  // 确保不会超出数组边界
  if (startIndex >= lessonData.value.length) {
    return []
  }
  
  return lessonData.value.slice(startIndex, endIndex)
}
// 获取通关状态
const getCompletionStatus = (knowledge: KnowledgePoint) => {
  return knowledge.isCompleted && knowledge.completionLevel
}

// 获取通关徽章图标
const getCompletionBadge = (knowledge: KnowledgePoint) => {
  if (knowledge.completionLevel === 'perfect' && (knowledge.basicProgress || 0) >= 100 && (knowledge.advancedProgress || 0) >= 100) {
    return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
  }
  return ''
}

// 判断是否可以显示挑战按钮
const canShowChallengeButton = (lesson: { knowledgePoints: KnowledgePoint[] }) => {
  // 检查课程中是否有知识点达到挑战条件
  return lesson.knowledgePoints.some((knowledge: KnowledgePoint) => 
    (knowledge.correctRate || 0) >= 70 && knowledge.studyStatus !== 1
  )
}

// 获取挑战按钮的状态和显示类型
const getChallengeButtonStatus = (lesson: { knowledgePoints: KnowledgePoint[] }) => {
  if (!lesson.knowledgePoints || lesson.knowledgePoints.length === 0) {
    return {
      type: 'challenge', // 显示挑战按钮
      image: null,
      showButton: true
    }
  }
  
  // 检查所有知识点的levelVos数据
  let allLevelVosCompleted = true // 所有levelVos数据status都等于1
  let hasWrongQuestions = false // 是否有错题
  
  for (const knowledge of lesson.knowledgePoints) {
    if (!knowledge.levelVos || knowledge.levelVos.length === 0) {
      // 如果没有levelVos数据，说明还没完成所有等级
      allLevelVosCompleted = false
      break
    }
    
    // 检查当前知识点的所有levelVos是否都完成
    for (const levelVo of knowledge.levelVos) {
      if (levelVo.status !== 1) {
        allLevelVosCompleted = false
        break
      }
    }
    
    if (!allLevelVosCompleted) {
      break
    }
    
    // 检查是否有错题（digestiveMarkers）
    if (knowledge.digestiveMarkers && knowledge.digestiveMarkers.length > 0) {
      hasWrongQuestions = true
    }
  }
  
  // 根据状态返回对应的显示类型
  if (allLevelVosCompleted) {
    if (hasWrongQuestions) {
      // 所有等级都完成但有错题，显示错题复习图片
      return {
        type: 'wrong_questions',
        image: new URL('@/assets/img/percision/training/ctxh.png', import.meta.url).href,
        showButton: false
      }
  } else {
      // 所有等级都完成且没有错题，显示完美完成图片
      return {
        type: 'perfect',
        image: new URL('@/assets/img/percision/training/wmgg.png', import.meta.url).href,
        showButton: false
      }
    }
  } else {
    // 还有等级未完成，显示挑战按钮
    return {
      type: 'challenge',
      image: null,
      showButton: true
    }
  }
}

// 获取挑战按钮文本（根据训练类型限制）
const getChallengeButtonText = (lesson: { knowledgePoints: KnowledgePoint[] }) => {
  const { min, max } = getAllowedLevelRange()
  
  if (!lesson.knowledgePoints || lesson.knowledgePoints.length === 0) {
    return `挑战${getLevelName(min)}`
  }
  
  // 检查每个知识点的levelVos第一条数据
  let allFirstLevelCompleted = true // 所有第一条数据status都等于1
  let allFirstLevelStatus2WithRate = true // 所有第一条数据status都等于2且correctRate>0
  let firstLevelValue: number | null = null // 第一条数据的level值，用于确定下一段位
  
  for (const knowledge of lesson.knowledgePoints) {
    if (!knowledge.levelVos || knowledge.levelVos.length === 0) {
      // 如果没有levelVos数据，默认需要挑战青铜
      allFirstLevelCompleted = false
      allFirstLevelStatus2WithRate = false
      break
    }
    
    const firstLevelVo = knowledge.levelVos?.[0]
    
    // 确保 firstLevelVo 存在
    if (!firstLevelVo) {
      allFirstLevelCompleted = false
      allFirstLevelStatus2WithRate = false
      break
    }
    
    // 记录第一条数据的level值（用于确定下一段位）
    if (firstLevelValue === null) {
      firstLevelValue = firstLevelVo.level
    }
    
    // 检查是否所有第一条数据status都等于1
    if (firstLevelVo.status !== 1) {
      allFirstLevelCompleted = false
    }
    
    // 检查是否所有第一条数据status都等于2且correctRate>0
    if (firstLevelVo.status !== 2 || (firstLevelVo.correctRate || 0) <= 0) {
      allFirstLevelStatus2WithRate = false
    }
  }
  
  // 优先级1: 如果所有知识点的第一条levelVos数据status都等于1，显示挑战下一段位
  if (allFirstLevelCompleted && firstLevelValue !== null) {
    // 确保下一段位不超过训练类型的最大等级
    const nextLevel = Math.min(firstLevelValue + 1, max)
    const nextLevelName = getLevelName(nextLevel)
    return `挑战${nextLevelName}`
  }
  
  // 优先级2: 如果所有知识点的第一条levelVos数据status都等于2且correctRate>0，显示"再次挑战当前段位"
  if (allFirstLevelStatus2WithRate && firstLevelValue !== null) {
    // 确保挑战等级在允许范围内
    const challengeLevel = Math.max(min, Math.min(firstLevelValue, max))
    const currentLevelName = getLevelName(challengeLevel)
    return `再次挑战${currentLevelName}`
  }
  
  // 优先级3: 根据第一条数据的level显示对应段位挑战
  if (firstLevelValue !== null) {
    // 确保挑战等级在允许范围内
    const challengeLevel = Math.max(min, Math.min(firstLevelValue, max))
    const currentLevelName = getLevelName(challengeLevel)

    return `挑战${currentLevelName}`
  }
  
  // 默认情况

  return `挑战${getLevelName(min)}`
}

// 获取星级图标
const getStarIcon = (filled: boolean) => {
  return filled
    ? new URL(`../../../assets/img/percision/star1.png`, import.meta.url).href
    : new URL(`../../../assets/img/percision/star0.png`, import.meta.url).href
}

// 获取操作按钮图标
const getActionIcon = (percentage: number) => {
  if (percentage > 90) {
    return new URL(`../../../assets/img/percision/strong.png`, import.meta.url).href
  }
  return new URL(`../../../assets/img/percision/pen.png`, import.meta.url).href
}

// 获取成就徽章图标
const getMedalIcon = (type: string) => {
  const medalMap = {
    'basic': 'medal_2.png',
    'advanced': 'medal_3.png',
    'comprehensive': 'medal_1.png'
  }
  return new URL(`../../../assets/img/percision/training/${medalMap[type] || 'medal_1.png'}`, import.meta.url).href
}
// 新增辅助函数
const getHexagonClass = (percentage: number) => {
  if (percentage >= 90) return 'excellent'
  if (percentage >= 70) return 'good'
  if (percentage >= 50) return 'average'
  return 'poor'
}

// 获取掌握度状态类名
const getMasteryClass = (percentage: number) => {
  if (percentage >= 90) return 'excellent'
  if (percentage >= 70) return 'good'
  if (percentage >= 50) return 'average'
  return 'poor'
}

// 获取掌握度文本
const getMasteryText = (percentage: number) => {
  if (percentage >= 90) return '优秀掌握'
  if (percentage >= 70) return '良好掌握'
  if (percentage >= 50) return '一般掌握'
  return '需要加强'
}

// 获取小奖牌图标
const getSmallMedalIcon = (type: number) => {
  const medalMap = {
    1: 'medal_small_1.png',
    2: 'medal_small_2.png',
    3: 'medal_small_3.png',
    4: 'medal_small_4.png',
    5: 'medal_small_5.png',
    6: 'medal_small_6.png',
    7: 'medal_small_7.png',
    8: 'medal_small_8.png'
  }
  return new URL(`../../../assets/img/percision/training/${medalMap[type] || 'medal_small_1.png'}`, import.meta.url).href
}

// 获取完美掌握奖牌
const getPerfectMedalIcon = () => {
  return new URL(`../../../assets/img/percision/training/medal_1.png`, import.meta.url).href
}

// 判断是否显示挑战徽章
const shouldShowChallengeBadge = (percentage1: number, percentage2: number) => {
  return percentage1 >= 70 && percentage2 < 70
}

// 获取挑战徽章图标
const getChallengeBadgeIcon = () => {
  return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
}

// 获取再次挑战徽章图标
const getRetryBadgeIcon = () => {
  return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
}

// 获取完美掌握徽章图标
const getPerfectBadgeIcon = () => {
  return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
}

const onMark = () => {
  router.push({
    path: '/ai_percision/knowledge_hotspot',
    query:{
      bookId:subjectObj.value.bookId,
      subject:query.subject,
      chapterId:chapterId.value,
      type:query.type
    }
  })
}
// 获取六边形背景类名
const getHexagonBgClass = (percentage: number, type: string) => {
  let baseClass = type
  if (percentage >= 90) {
    baseClass += ' excellent'
  } else if (percentage >= 70) {
    baseClass += ' good'
  } else if (percentage >= 50) {
    baseClass += ' average'
  } else {
    baseClass += ' poor'
  }
  return baseClass
}

// 获取知识点状态类名
const getKnowledgeStatusClass = (knowledge: KnowledgePoint) => {
  // 根据status值确定样式类名
  switch (knowledge.status) {
    case 1: return 'status-mastered'; // 已掌握
    case 2: return 'status-learning'; // 学习中
    case 3: return 'status-failed';   // 未掌握
    case 4: return 'status-testing';  // 测试中
    case 5: return 'status-untested'; // 未测试
    default: return 'status-unknown'; // 未知状态
  }
}

// 获取知识点状态文本
const getKnowledgeStatusText = (knowledge: KnowledgePoint) => {
  // 根据status值确定显示文本
  switch (knowledge.status) {
    case 1: return '已掌握';
    case 2: return '学习中';
    case 3: return '未掌握';
    case 4: return '测试中';
    case 5: return '未测试';
    default: return '未知';
  }
}

const onModify = () =>{
  console.log(learnUsers[0]?.learnId,"learnUserslearnUserslearnUsers")
    router.push({
    path: '/user/user_add',
    query: {
      learnId: learnUsers[0]?.learnId || '',
      pageType:'edit'
    }
  })
}

// 知识点详情弹窗相关
const knowledgeDetailVisible = ref(false)
const selectedKnowledge = ref<KnowledgePoint | null>(null)

const showKnowledgeDetail = (knowledge: KnowledgePoint) => {
  selectedKnowledge.value = knowledge
  knowledgeDetailVisible.value = true
}

const handleDetailClose = () => {
  knowledgeDetailVisible.value = false
  selectedKnowledge.value = null
}

// 获取知识点难度文本
const getDifficultyText = (point: KnowledgePoint) => {
  // 根据知识点的难度值返回对应的文本
  // 这里假设难度值在1-3之间，1为容易，2为中等，3为困难
  const difficultyLevel = point.difficulty || 1;
  
  switch (difficultyLevel) {
    case 1:
      return '容易';
    case 2:
      return '中等';
    case 3:
      return '困难';
    default:
      return '容易';
  }
}

// 格式化正确率显示：如果小数点后两位是00则显示整数
const formatCorrectRate = (rate: number): string => {
  if (rate === null || rate === undefined) {
    return '0';
  }
  
  // 转换为数字并保留两位小数
  const numRate = Number(rate);
  const fixedRate = numRate.toFixed(2);
  
  // 如果小数点后两位都是0，则返回整数
  if (fixedRate.endsWith('.00')) {
    return Math.floor(numRate).toString();
  }
  
  // 否则返回去掉末尾0的小数
  return parseFloat(fixedRate).toString();
}

// 根据训练类型获取允许的等级范围
const getAllowedLevelRange = () => {
  const trainingType = query.type
  if (trainingType === '1') {
    // 基础训练：青铜到白银 (1-2)
    return { min: 1, max: 2 }
  } else if (trainingType === '2') {
    // 提升训练：黄金到钻石 (3-4)
    return { min: 3, max: 4 }
  }
  // 默认基础训练
  return { min: 1, max: 2 }
}

// 根据训练类型过滤等级数据（保留level为null的项目显示灰色图片）
const getFilteredLevelVos = (levelVos: LevelVo[]) => {
  if (!levelVos || levelVos.length === 0) return []
  
  const { min, max } = getAllowedLevelRange()
  
  return levelVos.filter(levelVo => {
    // 如果level为null，保留该项目（显示灰色图片）
    if (levelVo.level === null || levelVo.level === undefined) {
      return true
    }
    // 如果level不为null，检查是否在允许的范围内
    return levelVo.level >= min && levelVo.level <= max
  })
}

// 获取等级名称
const getLevelName = (level: number | null) => {
  if (level === null || level === undefined) {
    return '未评级';
  }
  
  switch (level) {
    case 1:
      return '青铜';
    case 2:
      return '白银';
    case 3:
      return '黄金';
    case 4:
      return '钻石';
    default:
      return '未评级';
  }
}

// 获取下一个段位的名称（根据训练类型限制）
const getNextLevelName = (currentLevel: number | null) => {
  const { min, max } = getAllowedLevelRange()
  
  if (currentLevel === null || currentLevel === undefined) {
    return getLevelName(min);
  }
  
  // 如果当前等级已经是最高等级，返回当前等级名称
  if (currentLevel >= max) {
    return getLevelName(currentLevel);
  }
  
  // 返回下一个等级，但不超过最大等级
  const nextLevel = Math.min(currentLevel + 1, max);
  return getLevelName(nextLevel);
}

// 获取当前课程应该挑战的段位等级（用于API调用，根据训练类型限制）
const getChallengeLevel = (lesson: { knowledgePoints: KnowledgePoint[] }): number => {
  const { min, max } = getAllowedLevelRange()
  
  if (!lesson.knowledgePoints || lesson.knowledgePoints.length === 0) {
    return min; // 默认返回最小等级
  }
  
  // 检查每个知识点的levelVos第一条数据，确定挑战等级
  let allFirstLevelCompleted = true // 所有第一条数据status都等于1
  let allFirstLevelStatus2WithRate = true // 所有第一条数据status都等于2且correctRate>0
  let firstLevelValue: number | null = null // 第一条数据的level值
  
  for (const knowledge of lesson.knowledgePoints) {
    if (!knowledge.levelVos || knowledge.levelVos.length === 0) {
      // 如果没有levelVos数据，默认挑战青铜
      allFirstLevelCompleted = false
      allFirstLevelStatus2WithRate = false
      break
    }
    
    const firstLevelVo = knowledge.levelVos?.[0]
    if (!firstLevelVo) {
      allFirstLevelCompleted = false
      allFirstLevelStatus2WithRate = false
      break
    }
    
    // 记录第一条数据的level值
    if (firstLevelValue === null) {
      firstLevelValue = firstLevelVo.level
    }
    
    // 检查是否所有第一条数据status都等于1
    if (firstLevelVo.status !== 1) {
      allFirstLevelCompleted = false
    }
    
    // 检查是否所有第一条数据status都等于2且correctRate>0
    if (firstLevelVo.status !== 2 || (firstLevelVo.correctRate || 0) <= 0) {
      allFirstLevelStatus2WithRate = false
    }
  }
  
  // 优先级1: 如果所有知识点的第一条levelVos数据status都等于1，挑战下一段位
  if (allFirstLevelCompleted && firstLevelValue !== null) {
    const nextLevel = Math.min(firstLevelValue + 1, max) // 限制在允许的最高等级内
    return nextLevel
  }
  
  // 优先级2: 如果所有知识点的第一条levelVos数据status都等于2且correctRate>0，重新挑战当前段位
  if (allFirstLevelStatus2WithRate && firstLevelValue !== null) {
    // 确保挑战等级在允许范围内
    const challengeLevel = Math.max(min, Math.min(firstLevelValue, max))
    return challengeLevel
  }
  
  // 优先级3: 根据第一条数据的level挑战对应段位
  if (firstLevelValue !== null) {
    // 确保挑战等级在允许范围内
    const challengeLevel = Math.max(min, Math.min(firstLevelValue, max))
    return challengeLevel
  }
  
  // 默认挑战最小等级

  return min
}

// 获取等级图片
const getLevelImage = (level: number | null) => {
  if (level === null || level === undefined) {
    return new URL('@/assets/img/percision/training/huise.png', import.meta.url).href;
  }
  switch (level) {
    case 1:
      return new URL('@/assets/img/percision/training/qingt.png', import.meta.url).href;
    case 2:
      return new URL('@/assets/img/percision/training/baiyin.png', import.meta.url).href;
    case 3:
      return new URL('@/assets/img/percision/training/huangjin.png', import.meta.url).href;
    case 4:
      return new URL('@/assets/img/percision/training/zuanshi.png', import.meta.url).href;
    default:
      return new URL('@/assets/img/percision/training/huise.png', import.meta.url).href;
  }
}

// 获取等级图片
const getLeveltcImage = (level: number | null) => {
  if (level === null || level === undefined) {
    return new URL('@/assets/img/percision/training/huise.png', import.meta.url).href;
  }
  switch (level) {
    case 1:
      return new URL('@/assets/img/percision/training/medal_1.png', import.meta.url).href;
    case 2:
      return new URL('@/assets/img/percision/training/medal_2.png', import.meta.url).href;
    case 3:
      return new URL('@/assets/img/percision/training/medal_3.png', import.meta.url).href;
    case 4:
      return new URL('@/assets/img/percision/training/medal_4.png', import.meta.url).href;
    default:
      return new URL('@/assets/img/percision/training/medal_1.png', import.meta.url).href;
  }
}

// 获取挑战弹窗中当前挑战段位的图片
const getCurrentChallengeLevelImage = () => {
  if (!selectedLessonForChallenge.value) {
    return new URL('@/assets/img/percision/training/qingtong.png', import.meta.url).href; // 默认青铜
  }
  
  const challengeLevel = getChallengeLevel(selectedLessonForChallenge.value);
  return getLevelImage(challengeLevel);
}

// 计算属性：获取当前挑战的段位信息（根据训练类型）
const currentChallengeInfo = computed(() => {
  const { min } = getAllowedLevelRange()
  
  if (!selectedLessonForChallenge.value) {
    return {
      level: min,
      levelName: getLevelName(min),
      levelImage: getLeveltcImage(min)
    }
  }
  
  const challengeLevel = getChallengeLevel(selectedLessonForChallenge.value);
  const levelName = getLevelName(challengeLevel);
  const levelImage = getLeveltcImage(challengeLevel);
  
  return {
    level: challengeLevel,
    levelName,
    levelImage
  }
})

// 计算总题量
const getTotalQuestions = () => {
  if (!selectedLessonForChallenge.value || !selectedLessonForChallenge.value.knowledgePoints) {
    return 6; // 默认值
  }
  
  // 计算所有知识点的题量总和
  return selectedLessonForChallenge.value.knowledgePoints.reduce((total: number, point: KnowledgePoint) => {
    return total + (point.total || 3); // 如果没有题量数据，默认为3题
  }, 0);
}

// 计算预估完成时间（分钟）
const getEstimatedTime = () => {
  const totalQuestions = getTotalQuestions();
  // 假设每题平均需要1分钟
  return Math.max(Math.ceil(totalQuestions), 6); // 至少6分钟
}

// Watch for changes in options data - 备用的选中逻辑
watch(() => options.value, (newOptions) => {
  
  // 如果options数据变化但仍然没有chapterId，尝试再次自动选择
  if (newOptions && newOptions.length > 0 && !chapterId.value && !routeHasChapterId.value) {
    
    // 延迟执行，确保DOM完全渲染和树组件初始化完成
    setTimeout(() => {
      if (!chapterId.value) { // 再次检查，避免重复设置
        console.log("🚀 执行延迟自动选择")
        handleDefaultSelection()
      } else {
        console.log("✅ 已有chapterId，跳过自动选择")
      }
    }, 500) // 增加延迟时间，确保树组件完全初始化
  }
}, { immediate: true })

</script>

<style scoped lang="scss">
.training-page {
  display: flex;
  min-height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.left-sidebar {
  position: relative;
  background: white;
  border-radius: 0 20px 20px 0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

  .sidebar-title {
    position: absolute;
    left: -14px;
    top: 10px;
    width: 179px;
    height: 46px;
    line-height: 46px;
    text-align: center;
    background: linear-gradient(135deg, #00c9a3 0%, #00a085 100%);
    color: white;
    font-size: 20px;
    font-weight: 700;
    border-radius: 0 10px 10px 0;
    z-index: 10;

    .title-decoration {
      position: absolute;
      bottom: -14px;
      left: 0;
      width: 0;
      height: 0;
      border-left: 7px solid transparent;
      border-right: 7px solid #00886e;
      border-top: 7px solid #00886e;
      border-bottom: 7px solid transparent;
    }
  }
}
.test-box {
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 11.875rem);
  width: 54.5rem;
  margin-left: 1.5625rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: .625rem;
  position: relative;
  .test-wrap{
      width: calc(100% - .5rem);
      .hui-line{
        width: calc(100% - 1.75rem);
        border-bottom: .0625rem dashed #EAEAEA;
        margin: 0 0 0 .875rem;
        float: left;
      }
    }
    &-item {
      width: 100%;
      height: 6.875rem;
      display: flex;
      padding: 1.25rem 1.25rem 1.25rem 1.25rem;
      box-sizing: border-box;
      &:hover {
        background: #effdfb;
      }
      &-img {
        width: 3.1875rem;
        height: 100%;
        font-size: .75rem;
        background-image: url(@/assets/img/percision/test-img.png);
        background-size: 100%;
        background-repeat: no-repeat;
        text-align: center;
        span {
          display: inline-block;
          margin-top: 1.85rem;
          position: relative;
          left: .125rem;
        }
      }
    &-info {
      margin-left: 1rem;
      width: 40rem;
      margin-right: 1rem;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      &-title {
        color: #2a2b2a;
        font-size: 1rem;
        font-weight: 400;
      }
      &-data {
        div {
          height: 1.75rem;
          border-radius: .875rem;
          background: #fef8e9;
          color: #ef9d19;
          display: inline-block;
          box-sizing: border-box;
          padding: .375rem .75rem;
          font-size: .75rem;
          margin-right: .625rem;
        }
      }
    }
    &-btn {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      &-it {
        width: 5.25rem;
        height: 1.875rem;
        line-height: 1.875rem;
        border-radius: .25rem;
        font-size: .875rem;
        text-align: center;
        cursor: pointer;
        img {
          width: .875rem;
          height: .875rem;
        }
      }
    }
  }
  .learn-img {
    position: fixed;
    bottom: 1.875rem;
    left: 55%;
    width: 14.0625rem;
    height: 3.125rem;
  }
}
.btn {
  color: #ffffff;
  background: #00c9a3;
}
.grey-btn {
  background: #f5f5f5;
  color: #999999;
}
.red-text {
  color: #dd2a2a;
}
.blue-text {
  color: #009c7f;
}
.icon-sty {
  width: 1rem;
}
.main-content {
  flex: 1;
  margin-left: 10px;
  width: calc(100% - 378px);
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  border-radius: 20px;
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  background-color: white;
  .content-head{
    position: relative;
    background: url('@/assets/img/percision/training/head-bg.png')center center no-repeat;
    background-size: cover;
    height: 121px;
    padding: 0 20px;
    .head-body{
      position: relative;
      padding: 20px 0 0 0px;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .textbook{
        width: 24px;
        height: 30px;
        margin-right: 6px;
      }
      .head-title{
        margin-right: 10px;
      }
      .head-switch{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 92px;
        height: 27px;
        font-size: 14px;
        font-weight: 600;
        color: #fff;
        background: #00957f;
        border-radius: 14px;
        cursor: pointer;
      }
    }
    .catalogue{
      background: #fff;
      border-radius: 22px;
      margin-top: 20px;
      line-height: 33px;
      padding-left: 16px;
      display: flex;
      span{
        color: rgba(102, 102, 102, 1);
        font-size: 16px;
      }
      img{
        width: 14px;
        height: 9px;
        margin: 12px 12px 12px auto;
      }
    }
    .superficiality{
      position: absolute;
      right: 0;
      top:0;
      width: 120px;
      height: 39px;
      cursor: pointer;
    }
    .head-select{
      position: relative;
      margin: 22px 0 0 20px;
      width: 882px;
      height: 33px;
    }
    .head-select :deep(.el-select__wrapper) {
        border-radius: 15px;
    }
  }
  .tip-content{
    padding: 0 16px;
    display: flex;
    justify-content: flex-start;
    height: 44px;
    align-items: center;
    .tip-avatar{
      width: 28px;
      height: 28px;
      margin-right: 10px;
    }
    .tip-text{
      color: #3294DB;
      font-size: 12px;
    }
  }
  .content-tip{
    position: relative;
    margin: 10px auto;
    width: 882px;
    height: 44px;
    border-radius: 4px;
    background: rgba(236, 247, 255, 1);
    .tip-bg{
      position: absolute;
      top: 0;
      left: 0;
      width: 882px;
      height: 44px;
    }
  }
}

.knowledge-training-list {
  padding:0 20px;
  .lesson-section {
    margin-bottom: 30px;
    
    background: #FAFBFD;
    // border-radius: 8px;
    // overflow: hidden;

    .lesson-tag {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 24px;
      text-align: center;
      border: 1px solid #5A85EC;
      background: rgba(238, 243, 253, 1);
      color: rgba(90, 133, 236, 1);
      font-size: 12px;
      font-weight: 400;
      // border-radius: 0 0 8px 0;
    }
  }
}

.knowledge-info {
 
  .knowledge-box {
  display: flex;
    flex-direction: column;
    gap: 10px;
  }
}

.knowledge-item {
  display: flex;
  align-items: center;
  margin: 0 20px;
  padding: 10px 0 30px 0;
  position: relative;
  transition: all 0.2s ease;
  border-bottom: 1px dashed rgba(234, 234, 234, 1);
  
  &:last-child {
    border-bottom: none;
  }

  .knowledge-name {
    width: 280px;
    padding-right: 20px;
    border-right: 1px solid #EAEAEA;
    margin-right: 16px;

    .knowledge-title {
      font-size: 14px;
      font-weight: 400;
      color: #333;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2; /* 限制为两行 */
      line-clamp: 2; /* 标准属性，用于兼容性 */
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-right: 30px; /* 为状态标签留出空间 */
    }
  }

  .progress-area {
    display: flex;
    align-items: center;
    margin-right: 20px;

    .hexagon-group {
      display: flex;
      gap: 20px;
      align-items: center;
    }
  }

  .action-area {
    display: flex;
    gap: 8px;
  }
}

.hexagon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;

  .hexagon-bg {
    width: 50px;
    height: 50px;
    position: relative;
    background: url('@/assets/img/percision/training/medal_small_1.png')center no-repeat;
    background-size: cover;
    &.basic {
      // 基础训练 - 绿色系
      &.excellent {
        background: url('@/assets/img/percision/training/medal_small_2.png')center no-repeat;
      }
      &.good {
        background: url('@/assets/img/percision/training/medal_small_3.png')center no-repeat;

      }
      &.average {
        background: url('@/assets/img/percision/training/medal_small_4.png')center no-repeat;

      }
      &.poor {
        background: url('@/assets/img/percision/training/medal_small_5.png')center no-repeat;

      }
    }

    &.advanced {
      // 进阶训练 - 蓝色系
      &.excellent {
        background: url('@/assets/img/percision/training/medal_small_6.png')center no-repeat;

      }
      &.good {
        background: url('@/assets/img/percision/training/medal_small_7.png')center no-repeat;

      }
      &.average {
        background: url('@/assets/img/percision/training/medal_small_8.png')center no-repeat;
      }
      &.poor {
        background: url('@/assets/img/percision/training/medal_small_6.png')center no-repeat;

      }
    }

    .hexagon-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: white;

      .percentage-text {
        font-size: 10px;
        font-weight: 600;
        margin-bottom: 2px;
        font-size: 12px;
        padding: 3px;
        position: absolute;
        bottom: -50px;
        color: rgba(0, 156, 127, 1);
        left: 50%;
        transform: translateX(-50%);
        background: rgba(229, 249, 246, 1);
      }

      .medal-crown {
        position: absolute;
        top: -12px;
        left: 50%;
        transform: translateX(-50%);
        width: 16px;
        height: 16px;
      }
    }
  }
}

// 等级显示样式
.level-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;

  .level-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;

    .level-image {
      width: 60px;
      height: 50px;
      object-fit: contain;
    }

    .level-info {
      text-align: center;

      .level-name {
        font-size: 10px;
        color: #666;
        font-weight: 500;
        margin-bottom: 2px;
      }

      .level-rate {
        font-size: 12px;
        font-weight: 600;
        color: #00c9a3;
        padding: 2px 6px;
        background: rgba(229, 249, 246, 1);
        border-radius: 8px;
      }
    }
  }
}

// 按钮样式
.action-btn {
  display: flex;
  align-items: center;
  height: 19px;
  font-size: 14px;
  align-items: center;
  justify-content: space-between;
  color: #999999;
  cursor: pointer;
  .action-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    &.study-icon {
      background: url('@/assets/img/percision/pen.png') no-repeat center;
      background-size: contain;
      margin-right: 6px;
    }

    &.practice-icon {
      background: url('@/assets/img/percision/strong.png') no-repeat center;
      background-size: contain;
    }
  }

  &.study {
    background: #f8f9fa;
    padding-left: 6px;
    // border: 1px solid #e9ecef;
    text-decoration: underline;
    &:hover {
      // background: #e9ecef;
      color: #495057;
    }
  }

  &.practice {
    background: #f8f9fa;
    padding-left: 6px;
    // border: 1px solid #e9ecef;
    text-decoration: underline;
    &:hover {
      // background: #e9ecef;
      color: #495057;
    }
  }
}

// 成就徽章样式
.achievement-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  color: white;
  z-index: 2;

  &.challenge {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  }

  &.retry {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  }

  &.perfect {
    background: transparent;
    padding: 0;

    .perfect-icon {
      width: 60px;
      height: auto;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.lesson-section {
  animation: fadeInUp 0.5s ease-out;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
}

.knowledge-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.status-mastered {
  background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
}

.status-learning {
  background: linear-gradient(150.8deg, #5a85ec 0%, #3a65cc 100%);
}

.status-failed {
  background: linear-gradient(150.8deg, #f07f4c 0%, #c95656 100%);
}

.status-testing {
  background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
}

.status-untested {
  background: #bbbbbb;
}

.status-unknown {
  background: #999999;
}

.knowledge-detail-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
  line-height: 1.6;
  font-size: 14px;
  color: #333;
  
  :deep(table) {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
    
    td, th {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: center;
    }
    
    tr:nth-child(even) {
      background-color: #f2f2f2;
    }
  }
  
  :deep(br) {
    margin-bottom: 5px;
  }
}

.knowledge-name {
  cursor: pointer;
  
  &:hover .knowledge-title {
    color: #5a85ec;
    text-decoration: underline;
  }
}

.lesson-container {
  border: 1px solid #EAEAEA;
  overflow: hidden;
  background: #FFFFFF;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  position: relative;
}

.lesson-title {
  text-align: center;
  font-size: 12px;
  color: #5A85EC;
  width: 60px;
  line-height: 24px;
  border: 1px solid #5A85EC;
  background: rgba(238, 243, 253, 1);
}

.lesson-content {
  padding: 10px 0 0 0;
}

.empty-state {
  // display: flex;
  // justify-content: center;
  // align-items: center;
  height: 300px;
  border-radius: 8px;
  margin-top: 200px;
  width: 900px;
  text-align: center;
}

.empty-message {
  font-size: 16px;
  color: #999;
}

.challenge-area {
  position: absolute;
  top: 58%;
  transform: translateY(-50%);
  right: 20px;
  bottom: 20px;
}

.challenge-btn {
  width: 112px;
  height: 48px;
  background: url('@/assets/img/percision/training/challenge_btn_bg.png')center no-repeat;
  background-size: 100%;
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 1;
  transition: opacity 0.3s ease;
  
  &:hover {
    opacity: 0.9;
    transform: scale(1.02);
  }
}

.challenge-status-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 112px;
  height: 48px;
  
  .status-icon {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: transform 0.3s ease;
    
    &:hover {
      transform: scale(1.1);
    }
    
    // 针对不同状态图片的特殊样式
    &[alt="perfect"] {
      // 完美完成图片样式
      opacity: 1;
      filter: drop-shadow(0 4px 8px rgba(0, 201, 163, 0.3));
    }
    
    &[alt="wrong_questions"] {
      // 错题复习图片样式
      opacity: 1;
      filter: drop-shadow(0 4px 8px rgba(255, 152, 0, 0.3));
      cursor: pointer;
      
      &:hover {
        transform: scale(1.15);
        filter: drop-shadow(0 6px 12px rgba(255, 152, 0, 0.4));
      }
    }
  }
}
.page-header {
  margin-bottom: 18px;
  margin-top: 10px;
}

.breadcrumbs {
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
}

.breadcrumbs .back-link {
  color: #00bfa5;
  font-size: 16px;
  text-decoration: none;
}
.breadcrumbs .back-link:hover {
  color: #00bfa5;
}

.breadcrumb-separator {
  color: #c0c4cc;
  margin: 0 5px;
}

.breadcrumb-item {
  color: #606266;
}
.breadcrumb-item.active {
  color: #303133;
  font-weight: 500;
}
</style>
<style lang="scss" scoped>
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.elevate-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.elevate-ct {
  width: 812px;
  height: 516px;
  border-radius: 20px;
  position: relative;
  background:  url("@/assets/img/percision/training/ytzk.png") no-repeat;
  background-size: 100%;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}
.top-title{
  width: 160px;
  margin: 0 auto;
  padding-top: 86px;
  font-size: 16px;
  text-align: center;
  font-weight: bold;
  color: #fff; /* 内部为白色文字 */
  text-shadow: 
    1px 1px 0 rgba(217, 78, 50, 1),  
    -1px -1px 0 rgba(217, 78, 50, 1),  
    1px -1px 0 rgba(217, 78, 50, 1),  
    -1px 1px 0 rgba(217, 78, 50, 1); /* 外部为红色描边 */
  }
  .block-ct{
    width: 652px;
    height: 146px;background: rgba(255, 255, 255, 1);margin: 0 auto;margin-top: 20px;box-shadow: 0 0 3px 3px rgba(208, 192, 169, 1);border-radius: 14px;
    padding: 30px 50px 20px 30px;
    text-align: center;
    .book-list{
    padding: 0 20px 0 0px;
    display: flex;
    margin-bottom: 20px;
    img{
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
    .book-name{
      color: rgba(50, 58, 87, 1);
      font-size: 16px;
      width: 300px;
      text-align: left;
      white-space: nowrap;       /* 防止文字换行 */
      overflow: hidden;          /* 隐藏超出部分 */
      text-overflow: ellipsis; 
    }
    .book-tl{
      color: rgba(50, 58, 87, 1);
      font-size: 16px;
      padding-left: 46px;
      span{
        font-size: 20px;
        font-weight: 700;
        color: rgba(255, 151, 31, 1);
        padding-left: 10px;
      }
    }
  }
  .prompt{
    text-align: center;
    display: flex;
    color: rgba(42, 43, 42, 1);
    font-size: 16px;
    width: 260px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    display: inline-block;
    padding-top: 20px;
    span{
      font-size: 20px;
      color: rgba(255, 151, 31, 1);
    }
  }
  }
  .challenge-fq{
  display: flex;
  color: rgba(50, 58, 87, 1);
  justify-content: center;
  align-items: center;
  margin: 26px auto 0 auto;
  font-size: 16px;
  
  .challenge-level-icon {
    width: 66px;
    height: 30px;
    margin: 0 5px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: transform 0.3s ease;
    
    &:hover {
      transform: scale(1.1);
    }
  }
  
  img{
    width: 66px;
    height: 30px;
    margin: 0 5px;
  }
}
.book-challenge{
    width: 112px;
    height: 46px;
    background: url('@/assets/img/percision/training/challenge_btn_bg.png')center no-repeat;
    background-size: 100%;
    font-weight: 700;
    color: #fff;
    cursor: pointer;
    font-size: 16px;
    line-height: 46px;
    text-align: center;
    margin: 40px auto 0 auto;
  }
.click-bt{
 display: flex;
 cursor: pointer;
}

.close-btn {
  position: absolute;
  top: -10px;
  right: -20px;
  margin-top: 20px;
  border-radius: 50%;
  // background-color: rgba(0, 0, 0, 0.1);
  color: #333;
  font-size: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  img{
    width: 54px;
    height: 54px;
  }
}

.close-btn:hover {
  // background-color: rgba(0, 0, 0, 0.2);
  transform: scale(1.1);
}
</style>
