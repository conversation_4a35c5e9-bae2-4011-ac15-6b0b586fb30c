<!-- 我的积分 -->
<template>
    <div class="content">
        <div class="header">
            <div class="header-img">
                <img src="@/assets/img/percision/back_o.png" alt="back" @click="goBack"></img>
                <img src="@/assets/img/percision/olympiad_text.png" alt="text"></img>
            </div>
            <div class="header-text">
                {{ subjectObj.editionName }}{{ subjectObj.typeName }}
            </div>
        </div>
        <div style="display: flex">
            <div class="left" v-loading="loadingTree">
                <el-tree
                    ref="knowledgeTreeRef"
                    class="custom-tree"
                    node-key="id"
                    :props="defaultProps"
                    :data="treeData"
                    @node-click="setPointId"
                    :default-expand-all = "true"
                    >

                    <template #default="{ node, data }">
                        <div v-if="!node.isLeaf" class="custom-node" :class="node.expanded?'border-left':''">
                            <span class="tree-h1" >{{ node.label }}</span>
                            <el-icon class="expand-icon">
                                <ArrowDown v-if="node.expanded"></ArrowDown>
                                <ArrowUp v-else></ArrowUp>
                            </el-icon>
                        </div>
                        <div v-else class="custom-node isLeaf">
                            <span>{{ node.label }}</span>
                        </div>
                    </template>
                </el-tree>
            </div>
            <div class="main" v-loading="loading">
                <img class="record-sty" @click="goRecord" src="@/assets/img/percision/record.png" alt="record"></img>
                <div class="main-box">
                    <div class="main-box-flex">
                        <img class="way-start" src="@/assets/img/percision/final_start.png" alt="start"></img>
                        <div v-for="(item, index) in options">
                            <div v-if="index == 0" class="way-box">
                                <img class="way-sty" src="@/assets/img/percision/way1.png" alt=""></img>
                                <div class="island-box position-right">
                                    <img class="way-sty" :src="getIsland(index, item.status3)" alt=""></img>
                                    <img v-if="item.status3 == 1" class="way-status" src="@/assets/img/percision/olympiad_success.png" alt=""></img>
                                    <div v-if="item.status3 !== 0" class="way-rate-box">
                                        <img src="@/assets/img/percision/report.png" alt=""></img>
                                        正确率：<span class="way-rate">{{ item.correctRate }}</span>%
                                    </div>
                                    <div class="way-point-box">
                                        <div class="way-point-box-cont">
                                            {{ index + 1 }}. {{ item.name }}
                                        </div>
                                        <div v-if="item.status3 == 0" class="start-btn-box">
                                            <img @click="reChallenge(item)" src="@/assets/img/percision/start-btn.png" alt=""></img>
                                        </div>
                                        <div v-if="item.status3 == 2" class="way-point-box-btn">
                                            <div @click="reChallenge(item)">再次闯关</div>
                                            <div @click="goLearning(item)"><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else-if="index % 2 !== 0" class="way-box">
                                <img class="way-sty" src="@/assets/img/percision/way2.png" alt=""></img>
                                <div class="island-box position-left top50">
                                    <img class="way-sty" :src="getIsland(index, item.status3)" alt=""></img>
                                    <img v-if="item.status3 == 1" class="way-status" src="@/assets/img/percision/olympiad_success.png" alt=""></img>
                                    <img v-if="showChain(item.status3, index)" class="way-status-chain" src="@/assets/img/percision/chain.png" alt=""></img>
                                    <div v-if="item.status3 !== 0" class="way-rate-box text-right">
                                        <img src="@/assets/img/percision/report.png" alt=""></img>
                                        正确率：<span class="way-rate">{{ item.correctRate }}</span>%
                                    </div>
                                    <div class="way-point-box">
                                        <div class="way-point-box-cont">
                                            {{ index + 1 }}. {{ item.name }}
                                        </div>
                                        <div v-if="showStart(item.status3, index)" class="start-btn-box">
                                            <img @click="reChallenge(item)" src="@/assets/img/percision/start-btn.png" alt=""></img>
                                        </div>
                                        <div v-if="item.status3 == 2" class="way-point-box-btn">
                                            <div @click="reChallenge(item)">再次闯关</div>
                                            <div @click="goLearning(item)"><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else-if="index % 2 === 0" class="way-box">
                                <img class="way-sty" src="@/assets/img/percision/way3.png" alt=""></img>
                                <div class="island-box position-right top50">
                                    <img class="way-sty" :src="getIsland(index, item.status3)" alt=""></img>
                                    <img v-if="item.status3 == 1" class="way-status" src="@/assets/img/percision/olympiad_success.png" alt=""></img>
                                    <img v-if="showChain(item.status3, index)" class="way-status-chain" src="@/assets/img/percision/chain.png" alt=""></img>
                                    <div v-if="item.status3 !== 0" class="way-rate-box">
                                        <img src="@/assets/img/percision/report.png" alt=""></img>
                                        正确率：<span class="way-rate">{{ item.correctRate }}</span>%
                                    </div>
                                    <div class="way-point-box">
                                        <div class="way-point-box-cont">
                                        {{ index + 1 }}. {{ item.name }}
                                        </div>
                                        <div v-if="showStart(item.status3, index)" class="start-btn-box">
                                            <img @click="reChallenge(item)" src="@/assets/img/percision/start-btn.png" alt=""></img>
                                        </div>
                                        <div v-if="item.status3 == 2" class="way-point-box-btn">
                                            <div @click="reChallenge(item)">再次闯关</div>
                                            <div @click="goLearning(item)"><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <PassGifDialog ref="passGifDialog" :isFinal="false" />
</template>
  
<script lang="ts" setup>
import router from '@/router'
import { dataEncrypt } from '@/utils/secrets'
import PassGifDialog from '@/views/ai_percision/finalQuestion/components/pass_gif_dialog.vue'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { getImprovementPointList } from "@/api/video"
import { sonPointListApi } from "@/api/point"
import { nextTick, onMounted, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from "@/store/modules/user"
const userStore = useUserStore()
const { subjectObj } = storeToRefs(userStore)
const passGifDialog = ref()
const knowledgeTreeRef = ref() // 添加ref用于访问知识树组件
const options =  ref([] as any[])
const treeData =  ref([] as any[])
const defaultProps = {
	value: 'id',
    label: 'name',
    children: 'children'
}
const loading = ref(false)
const loadingTree = ref(false)
const pointId = ref()

onMounted(async() => {
    await getPointTree()
    await getPointlist()
})
const getPointTree = async () => {
    loadingTree.value = true
    const res: any = await getImprovementPointList({
        subject: subjectObj.value.id,
        hierarchy:2
    })
    if(res.code == 200) {
        treeData.value = res.data || []
        pointId.value = res.data[0]?.children[0]?.id
        nextTick(() => {
            knowledgeTreeRef.value.setCurrentKey(pointId.value, true)
        })

    }
    loadingTree.value = false
}
const getPointlist = async() => {
    loading.value = true
    const res: any = await sonPointListApi({
        pointId: pointId.value,
        type: 4
    })
    if(res.code == 200) {
      options.value = res.data || []
      if (res.data[res.data.length - 1].status3 == 1) {
        passGifDialog.value.init()
      }
    }
    loading.value = false
}
const showChain = (status3: number, index: number) => {
    if (status3 == 0) {
        if (options.value[index - 1].status3 == 1) {
            return false
        } else {
            return true
        }
    } else{
        return false
    }
}
const showStart = (status3: number, index: number) => {
    if (status3 == 0) {
        if (options.value[index - 1].status3 == 1) {
            return true
        } else {
            return false
        }
    } else{
        return false
    }
}
const goBack = () => {
    router.push({
        path: '/ai_percision/knowledge_graph'
    })
}
const setPointId = (nodeData: any) => {
    if (nodeData.children.length > 0) return
    pointId.value = nodeData.id
    getPointlist()
}
const getIsland = (index: number, status3: number) => {
    const sign = index % 4
    const lastStaus = options.value[index - 1]?options.value[index - 1].status3: null
    let img = ""
    if (status3 == 1 || (lastStaus == 1 && status3 == 0) || lastStaus == null) {
        switch (sign) {
            case 0:
                img = "isplanet1.png"
                break
            case 1:
                img = "isplanet2.png"
                break
            case 2:
                img = "isplanet3.png"
                break
            case 3:
                img = "isplanet4.png"
                break
        }
    } else {
        switch (sign) {
            case 0:
                img = "isplanet1_grey.png"
                break
            case 1:
                img = "isplanet2_grey.png"
                break
            case 2:
                img = "isplanet3_grey.png"
                break
            case 3:
                img = "isplanet4_grey.png"
                break
        }
    }
    return new URL(`../../../assets/img/percision/${img}`, import.meta.url).href //静态资源引入为url，相当于require()

}
const reChallenge = (data: any) => { 
    router.push({
        path: '/ai_percision/olympiad_question/olympiad_question_write',
        query: {
            data: dataEncrypt({
                pointId: data.id,
                parentPointId: pointId.value,
                pageSource: '12'
            })
        }
    })
}
const goRecord = (data: any) => { 
    router.push({
        path: '/ai_percision/olympiad_question/olympiad__record',
        query: {
            data: dataEncrypt({
                parentPointId: pointId.value,
                pageSource: '12'
            })
        }
    })
}

const goLearning = (data: any) => { 
    router.push({
        path: '/ai_percision/olympiad_question/olympiad__learning',
        query: {
            data: dataEncrypt({
                // sourceId: pointId.value,
                // pointId: pointId.value,
                pointId: [data.id],
                subject: subjectObj.value.id
            })
        }
    })
}
</script>
  
<style lang="scss" scoped>
.content{
    width: 100%;
    height: calc(100vh - 70px);
    background: url(@/assets/img/percision/olympiadbg.png) no-repeat;
    background-size: 100% calc(100vh - 70px);
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: column;
    .header {
        width: 1300px;
        height: 93px;
        display: flex;
        padding-top: 30px;
        box-sizing: border-box;
        justify-content: space-between;
        &-img {
            display: flex;
            align-items: center;
            img:first-child {
                width: 44px;
                height: 32px;
                margin-right: 16px;
                cursor: pointer;
            }
            img:last-child {
                width: 205px;
                height: 53px;
            }
        }
        &-text {
            height: fit-content;
            border-radius: 22px;
            background: #0000004d;
            color: #ffffff;
            font-size: 16px;
            font-weight: 400;
            padding: 11px 20px;
        }
    }
    .left {
        width: 368px;
        height: calc(100vh - 163px);
        background: #ffffff;
        box-sizing: border-box;
        margin-right: 10px;
        padding: .875rem;
        overflow-y: auto;
    }
    .main {
        width: 922px;
        height: calc(100vh - 163px);
        background: #ffffff;
        padding: 20px 20px 0 20px;
        box-sizing: border-box;
        position: relative;
        .record-sty {
            position: absolute;
            bottom: 50px;
            right: 70px;
            width: 87px;
            height: 80px;
            cursor: pointer;
            z-index: 100;
        }
        &-box {
            height: calc(100vh - 183px);
            width: 100%;
            background: url(@/assets/img/percision/olympiadbg2.png) no-repeat;
            background-size: 100% 100%;
            display: flex;
            overflow-y: auto;
            justify-content: center;
            .main-box-flex {
                width: 684px;
                margin-top: 10px;
                padding: 0 76px;
                box-sizing: border-box;
            }
            .way-start {
                width: 97px;
                height: 65px;
                margin-left: 26px;
                margin-bottom: -5px;
            }
            .way-box {
                position: relative;
                .way-sty {
                    width: 534px;
                }
                .island-box {
                    position: absolute;
                    top: 0;
                    .way-sty {
                        width: 190px;
                        height: 190px;
                        position: relative;
                        z-index: 10;
                    }
                    .way-status {
                        position: absolute;
                        top: -50px;
                        left: 0;
                        width: 190px;
                        height: 190px;
                        z-index: 11;
                    }
                    .way-status-chain {
                        position: absolute;
                        width: 170px;
                        left: 11px;
                        top: 60px;
                        z-index: 100;
                    }
                    .way-rate-box {
                        position: absolute;
                        z-index: 1;
                        width: 170px;
                        top: 90px;
                        right: 150px;
                        background-color: #ffffff;
                        padding: 6px 12px;
                        font-size: 14px;
                        border-radius: 16px;
                        display: flex;
                        align-items: center;
                        span {
                            font-weight: 700;
                        }
                        img {
                            width: 16px;
                            height: 16px;
                            margin-right: 5px;
                        }
                    }
                    .text-right {
                        left: 150px;
                        width: 120px;
                        padding-left: 60px;
                    }
                    .way-point-box {
                        position: absolute;
                        top: 143px;
                        z-index: 20;
                        &-cont {
                            padding: 8px 12px;
                            width: 200px;
                            text-align: center;
                            box-sizing: border-box;
                            min-height: 30px;
                            border-radius: 10px;
                            border: 2px solid #5a85ec;
                            background: #ffffffcc;
                            color: #323a57;
                        }
                        &-btn {
                            display: flex;
                            justify-content: space-between;
                            margin-top: 6px;
                            div {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 90px;
                                cursor: pointer;
                                height: 29px;
                                border-radius: 14.5px;
                                border: 2px solid #f25500;
                                background: #e98b00;
                                color: #ffffff;
                                font-size: 14px;
                                img {
                                    width: 16px;
                                    height: 16px;
                                    margin-right: 5px;
                                }
                            }
                        }
                    }
                }
                .position-right {
                    right: -60px;
                }
                .position-left {
                    left: -60px;
                }
                .top50 {
                    top: 58px;
                }
            }
        }
    }
}

.custom-tree {
  color: #666666;
  font-size: 1rem;
  .title-h1 {
    color: #2a2b2a;
    background: #f5f5f5;
  }
  .border-left {
    border-left: .1875rem solid #00C9A3;
  }
  /* 隐藏默认图标 */
  :deep(.el-tree-node__expand-icon) {
    display: none;
  }
  :deep(.el-tree-node) {
    width: 100%;
    margin: .5rem 0;  /* 增加节点间距 */
    position: relative;

    & > .el-tree-node__content {
      height: 2.5625rem;
      line-height: 2.5625rem;
    }
  }
  /* 自定义节点布局 */
  .custom-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 .4375rem!important;
    box-sizing: border-box;
    span {
      display: inline-block;
      width: calc(100% - 5.3125rem);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  /* 右侧展开图标 */
  .expand-icon {
    margin-left: .5rem;
    font-size: .875rem;
    color: #666;
  }
}
:deep(.el-tree-node__children) {
    .is-current {
      .el-tree-node__content {
        background: #e5f9f6!important;
      }
    }
}
.start-btn-box {
    width: 200px;
    display: flex;
    justify-content: center;
    margin-top: 6px;
    img {
        width: 114px;
        height: 42px;
        cursor: pointer;
    }
}
</style>
  