<template>
  <div class="knowledge-hotspot-page">
    <!-- 规则说明 -->
    <div class="rule-notice">
      <div class="notice-icon"></div>
      <span class="notice-text">
        规则：知识点标熟后，每个知识点出一道题进行检测，过关后确认为"已掌握"，反之"未掌握"。每个知识点仅有一次标熟测试的机会。
      </span>
    </div>

    <!-- 知识点章节列表 -->
    <div class="knowledge-sections">
      <div
        v-for="(section,index) in knowledgeSections"
        :key="index"
        class="knowledge-section"
      >
        <!-- 一级标题 - 始终展开，不可折叠 -->
        <div class="section-header">
          <h2 class="section-title">{{ section.title }}</h2>
        </div>

        <!-- 一级标题下的内容 -->
        <div class="section-content">
          <div
            v-for="(subSection, index) in section.subSections"
            :key="`${section.id}-sub-${index}`"
            class="sub-section"
          >
            <!-- 二级标题 - 可以折叠 -->
            <div
              class="sub-section-header"
              @click="toggleSubSection(`${section.id}-sub-${index}`)"
            >
              <h3 class="sub-section-title">{{ subSection.title }}</h3>
              <div
                class="toggle-icon"
                :class="{ expanded: expandedSubSections[`${section.id}-sub-${index}`] }"
              >
                <!-- <div class="arrow"></div> -->
              </div>
            </div>

            <!-- 二级标题下的知识点列表 -->
            <div
              v-show="expandedSubSections[`${section.id}-sub-${index}`]"
              class="sub-section-content"
            >
              <div v-if="subSection.points && subSection.points.length > 0" class="knowledge-points-grid">
                <div
                  v-for="point in subSection.points"
                  :key="point.id"
                  class="knowledge-point-item"
                  :class="{
                    'is-hotspot': point.isHotspot,
                    'status-marked': point.status === 0,
                    'status-mastered': point.status === 1,
                    'status-unmastered': point.status === 2
                  }"
                >
                  <div class="point-content">
                    <span class="point-name">{{ point.name }}</span>

                    <div class="point-actions">
                      <!-- 状态图标显示 -->
                      <!-- <div v-if="point.status !== undefined && point.status !== -1" class="status-indicator">
                        <img v-if="point.status === 0" class="status-icon" src="@/assets/img/percision/standard-stared.png" alt="已标记" />
                        <img v-else-if="point.status === 1" class="status-icon" src="@/assets/img/percision/training/understood.png" alt="已掌握" />
                        <img v-else-if="point.status === 2" class="status-icon" src="@/assets/img/percision/training/not-understood.png" alt="未掌握" />
                        <span class="status-text">
                          {{ getStatusText(point.status) }}
                        </span>
                      </div> -->

                        <template v-if="point.status == 0">
                          <div class="hotspot-indicator">
                            <div class="star-icon active"></div>
                            <span class="status-text">已标熟</span>
                          </div>
                          <button
                            class="cancel-btn"
                            @click="cancelHotspot(point.id)"
                          >
                            取消
                          </button>
                        </template>
                        
                        <img v-else-if="point.status === 1" class="status-icon" src="@/assets/img/percision/training/understood.png" alt="已掌握" />
                        <img v-else-if="point.status === 2" class="status-icon" src="@/assets/img/percision/training/not-understood.png" alt="未掌握" />
                        <template v-else>
                          <button
                            class="mark-btn"
                            @click="markAsHotspot(point.id)"
                          >
                            <div class="star-icon"></div>
                            <span>标熟</span>
                          </button>
                        </template>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="empty-points-message">
                该章节下暂无可标熟的知识点
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-action-bar">
      <div 
        class="action-button" 
        :class="{ 'disabled': hotspotCount === 0 }"
        @click="startEvaluation"
      >
        <span class="button-text">{{ hotspotCount }}个已标熟，去测评</span>
      </div>
    </div>
    
    <!-- 加载遮罩层 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>正在加载数据...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter ,useRoute} from 'vue-router'
import { dataEncrypt, dataDecrypt } from "@/utils/secret"

import { getPointCategoryApi, categoryAddApi, addTraininsgApi,categoryDelApi} from "@/api/precise"

const router = useRouter()
const route = useRoute()
const query = reactive<any>(route.query)

// 临时定义，稍后会被更新
let getUniqueHotspotIds = (): string[] => {
  return []
}

// 知识点数据结构
interface KnowledgePoint {
  id: string
  name: string
  isHotspot: boolean
  status?: number // -1: 未标记, 0: 已标记, 1: 已掌握, 2: 未掌握
}

interface SubSection {
  title: string
  points: KnowledgePoint[]
}

interface KnowledgeSection {
  id: string
  title: string
  subSections: SubSection[]
}

// 二级标题的折叠展开状态管理（只有二级标题可以折叠）
const expandedSubSections = reactive<Record<string, boolean>>({
  'section1-sub-0': true,
  'section1-sub-1': false,
  'section2-sub-0': false,
  'section2-sub-1': false
})

// 知识点章节数据
const knowledgeSections = ref<KnowledgeSection[]>([
  {
    id: 'section1',
    title: '一、一级标题一级标题一级标题一级标题',
    subSections: [
      {
        title: '二级标题二级标题 > 三级标题',
        points: [
          { id: 'point1-1', name: '知识点名称', isHotspot: false },
          { id: 'point1-2', name: '知识点名称', isHotspot: false },
          { id: 'point1-3', name: '知识点名称', isHotspot: false },
          { id: 'point1-4', name: '知识点名称', isHotspot: false }
        ]
      },
      {
        title: '二级标题二级标题 > 三级标题三级标题三级标题',
        points: [
          { id: 'point1-5', name: '知识点名称知识点名称知识点...', isHotspot: false },
          { id: 'point1-6', name: '知识点名称知识点名称', isHotspot: false },
          { id: 'point1-7', name: '知', isHotspot: false },
          { id: 'point1-8', name: '知识点名称知识点名称知识点名称知识点...', isHotspot: false },
          { id: 'point1-9', name: '知识点名称知', isHotspot: false },
          { id: 'point1-10', name: '知识点', isHotspot: false },
          { id: 'point1-11', name: '知识点', isHotspot: false },
          { id: 'point1-12', name: '知识点名称', isHotspot: false },
          { id: 'point1-13', name: '知识点名称', isHotspot: false },
          { id: 'point1-14', name: '知识点名称知识点名称', isHotspot: false },
          { id: 'point1-15', name: '知', isHotspot: false }
        ]
      }
    ]
  },
  {
    id: 'section2',
    title: '二、一级标题一级标题一级标题一级标题',
    subSections: [
      {
        title: '二级标题二级标题 > 三级标题',
        points: [
          { id: 'point2-1', name: '知识点名称知识点名称知识点名称', isHotspot: false },
          { id: 'point2-2', name: '知识点名称知识点名称', isHotspot: false },
          { id: 'point2-3', name: '知识点名称知识点名称', isHotspot: false }
        ]
      },
      {
        title: '二级标题二级标题 > 三级标题三级标题三级标题',
        points: [
          { id: 'point2-4', name: '知识点名称', isHotspot: false },
          { id: 'point2-5', name: '知识点名称', isHotspot: false },
          { id: 'point2-6', name: '知识', isHotspot: false },
          { id: 'point2-7', name: '知识点名称知识点名称', isHotspot: false }
        ]
      }
    ]
  }
])

// 更新getUniqueHotspotIds函数实现
getUniqueHotspotIds = (): string[] => {
  // 使用Set自动去重，即使数据中有重复ID，也只会计算一次
  const hotspotIdsSet = new Set<string>()
  
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      subSection.points.forEach(point => {
        // 只收集状态为0（已标记）的知识点
        if (point.status === 0 && point.id) {
          hotspotIdsSet.add(point.id)
        }
      })
    })
  })
  
  // 返回去重后的ID数组
  return Array.from(hotspotIdsSet)
}

// 记录唯一ID和重复ID
const logUniqueAndDuplicateIds = () => {
  // 确保knowledgeSections已初始化
  if (!knowledgeSections.value || knowledgeSections.value.length === 0) {
    console.log('知识点数据尚未初始化')
    return { uniqueIds: [], duplicateIds: [] }
  }
  
  // 收集所有知识点ID
  const allIds: string[] = []
  const uniqueIds: string[] = []
  const duplicateIds: string[] = []
  
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      subSection.points.forEach(point => {
        if (point.id) {
          if (allIds.includes(point.id)) {
            if (!duplicateIds.includes(point.id)) {
              duplicateIds.push(point.id)
            }
          } else {
            allIds.push(point.id)
            uniqueIds.push(point.id)
          }
        }
      })
    })
  })
  
  console.log(`唯一ID数量: ${uniqueIds.length}, 重复ID数量: ${duplicateIds.length}`)
  if (duplicateIds.length > 0) {
    console.log('重复ID列表:', duplicateIds)
  }
  
  return { uniqueIds, duplicateIds }
}

// 加载状态
const loading = ref(false)

// 计算已标熟的数量
const hotspotCount = computed(() => {
  // 确保knowledgeSections已初始化
  if (!knowledgeSections.value || knowledgeSections.value.length === 0) {
    return 0
  }
  
  // 使用辅助函数获取唯一的已标记知识点ID
  const uniqueIds = getUniqueHotspotIds()
  const count = uniqueIds.length
  
  console.log('已标熟知识点数量(唯一ID):', count)
  return count
})

// 监听hotspotCount变化
watch(hotspotCount, (newCount, oldCount) => {
  console.log(`hotspotCount变化: ${oldCount} -> ${newCount}`)
})

// 切换二级标题的折叠展开状态
const toggleSubSection = (subSectionId: string) => {
  expandedSubSections[subSectionId] = !expandedSubSections[subSectionId]
}

// 获取知识点的父级章节ID
const getParentChapterId = (pointId: string): string | null => {
  for (const section of knowledgeSections.value) {
    for (const subSection of section.subSections) {
      const point = subSection.points.find(p => p.id === pointId)
      if (point) {
        console.log(`知识点 ${pointId} 的父级章节ID: ${section.id}`)
        return section.id
      }
    }
  }
  console.warn(`未找到知识点 ${pointId} 的父级章节`)
  return null
}

// 标熟操作
const markAsHotspot = async (pointId: string) => {
  try {
    // 获取知识点的父级章节ID
    const parentChapterId = getParentChapterId(pointId)
    
    if (!parentChapterId) {
      ElMessage.error('无法获取知识点的章节信息')
      return
    }
    
    await ElMessageBox.confirm(
      '确定要将此知识点标记为已熟练吗？',
      '确认标熟',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 添加标熟
    const pointIdArr = pointId.split(',')
    
    // 构建chapterPoints数组，包含章节ID和知识点ID
    const chapterPoints = pointIdArr.map(pId => ({
      chapterId: parentChapterId,
      pointId: pId
    }))
    
    console.log('标熟操作参数:', {
      bookId: query.bookId,
      chapterPoints: chapterPoints,
      type: 1,
      subject: query.subject
    })
    
    categoryAddApi({
      bookId: query.bookId,
      chapterPoints: chapterPoints, // 传递包含章节ID和知识点ID的数组
      type: query.type,
      subject: query.subject
    }).then((res) => {
      if (res.code === 200) {
        console.log(res,"新增成功！")
        console.log(`知识点 ${pointId} 已标熟，所属章节: ${parentChapterId}`)
        // getBookList()
      } 
    })
    
    // 找到对应的知识点并更新状态
    knowledgeSections.value.forEach(section => {
      section.subSections.forEach(subSection => {
        const point = subSection.points.find(p => p.id === pointId)
        if (point) {
          point.isHotspot = true
          // 更新状态为已标记
          point.status = 0
          console.log(`已更新知识点 ${pointId} 状态为已标熟`)
        }
      })
    })

    ElMessage.success('标熟成功！')
  } catch {
    // 用户取消操作
  }
}

// 取消标熟操作
const cancelHotspot = async (pointId: string) => {
  try {
    // 获取知识点的父级章节ID
    const parentChapterId = getParentChapterId(pointId)
    
    if (!parentChapterId) {
      ElMessage.error('无法获取知识点的章节信息')
      return
    }
    
    await ElMessageBox.confirm(
      '确定要取消此知识点的标熟状态吗？',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 取消标熟
    const pointIdArr = pointId.split(',')
    
    // 构建chapterPoints数组，包含章节ID和知识点ID
    const chapterPoints = pointIdArr.map(pId => ({
      chapterId: parentChapterId,
      pointId: pId
    }))
    
    console.log('取消标熟操作参数:', {
      bookId: query.bookId,
      chapterPoints: chapterPoints,
      type: 1,
      subject: query.subject
    })
    
    categoryDelApi({
      bookId: query.bookId,
      chapterPoints: chapterPoints, // 传递包含章节ID和知识点ID的数组
      type: 1,
      subject: query.subject
    }).then((res) => {
      if (res.code === 200) {
        console.log(res,"删除成功！")
        console.log(`知识点 ${pointId} 已取消标熟，所属章节: ${parentChapterId}`)
      } 
    })
    
    // 找到对应的知识点并更新状态
    knowledgeSections.value.forEach(section => {
      section.subSections.forEach(subSection => {
        const point = subSection.points.find(p => p.id === pointId)
        if (point) {
          point.isHotspot = false
          // 重置状态为未标记
          point.status = -1
          console.log(`已更新知识点 ${pointId} 状态为未标记`)
        }
      })
    })

    ElMessage.success('取消标熟成功！')
  } catch {
    // 用户取消操作
  }
}

// 开始评估
const startEvaluation = () => {
  if (hotspotCount.value === 0) {
    ElMessage.warning('请至少标记一个知识点为已熟练')
    return
  }
 
  // 获取唯一的已标记知识点ID
  const uniqueArr = getUniqueHotspotIds()
  console.log(uniqueArr, "唯一知识点ID数组", uniqueArr.length);

  // 知识点标熟
  addTraininsgApi({
    bookId: query.bookId,
    pointIds: uniqueArr,
    type: query.type,
    subject:query.subject
  }).then((res:any) => {
    if (res.code === 200) {
      if(res.data){
        router.push({
        path: '/ai_percision/answer_training',
        query: {
          data: dataEncrypt({
            reportId: res.data ,
            pageSource: '1',
            bookId: query.bookId,
            chapterId:query.chapterId,
            source:'ripe'
          }),
        }
      })
        //   router.push({
        //   path: '/ai_percision/answer_training',
        //   query: {
        //     reportId: res.data,
        //   }
        // })
      }
      console.log(res.data,"创建成功1111！")

    } 
  })

}

// 返回上一页
const goBack = () => {
  window.history.back()
}
  // 自定义返回方法
  const customGoBack = () => {
      router.go(-1)
  }
  onUnmounted(() => {
    // 清除自定义返回方法
    if (window.customGoBack) {
      delete window.customGoBack
    }
  })
onMounted(() => {
  window.customGoBack = customGoBack
  console.log('知识点标热页面已加载')
  // 获取数据
  getBookList()
  // 初始化后检查知识点状态
  setTimeout(() => {
    console.log('初始化后检查知识点状态:')
    logAllPointsStatus()
  }, 1000)
})

//获取列表
const getBookList = async() => {
  try {
    loading.value = true // 显示加载状态
    console.log(query.bookId,"query")
    const res: any = await getPointCategoryApi({
      bookId:query.bookId,
      type: 1,
    })
    
    // 处理接口返回的数据结构
    if (res.data && res.data.chapters && Array.isArray(res.data.chapters)) {
      // 转换数据结构
      const processedData = processChaptersData(res.data.chapters)
      knowledgeSections.value = processedData
      
      // 检查是否有重复的知识点ID
      checkDuplicatePointIds()
      
      // 记录唯一ID和重复ID
      logUniqueAndDuplicateIds()
      
      // 同步所有知识点的isHotspot和status
      syncAllPointsHotspotStatus()
      
      // 记录所有知识点状态
      setTimeout(() => {
        logAllPointsStatus()
      }, 500)
    } else {
      console.warn('API返回的数据结构不符合预期', res.data)
      knowledgeSections.value = []
    }
    
    console.log(res,"resresresres")
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取章节数据失败')
    knowledgeSections.value = []
  } finally {
    // 延迟关闭loading，确保DOM渲染完成
    setTimeout(() => {
      loading.value = false
    }, 300)
  }
}

// 处理章节数据
const processChaptersData = (chapters) => {
  if (!chapters || !Array.isArray(chapters)) return []
  
  return chapters.map((chapter, index) => {
    return {
      id: chapter.id || `section${index + 1}`,
      title: `${index + 1}、${chapter.name || '未命名章节'}`,
      subSections: processSubSections(chapter.children || [])
    }
  })
}

// 处理子章节数据
const processSubSections = (subChapters) => {
  if (!subChapters || !Array.isArray(subChapters)) return []
  
  return subChapters.map((subChapter) => {
    return {
      title: subChapter.name || '未命名小节',
      points: processKnowledgePoints(subChapter.points || [])
    }
  })
}

// 处理知识点数据，只保留type=1的知识点
const processKnowledgePoints = (points) => {
  if (!points || !Array.isArray(points)) return []
  
  // 过滤type=1的知识点
  return points
    .filter(point => point.type === 1)
    .map(point => {
      // 确保状态和isHotspot同步
      const status = point.status !== undefined ? point.status : -1 // 默认为-1（未标记）
      const isHotspot = status === 0 // 只有status为0时，isHotspot才为true
      
      return {
        id: point.id || '', // 保留原始ID，不生成新ID
        name: point.name || '未命名知识点',
        isHotspot: isHotspot,
        status: status
      }
    })
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 0:
      return '已标记'
    case 1:
      return '已掌握'
    case 2:
      return '未掌握'
    default:
      return '未标记'
  }
}

// 记录所有知识点状态
const logAllPointsStatus = () => {
  // 确保knowledgeSections已初始化
  if (!knowledgeSections.value || knowledgeSections.value.length === 0) {
    console.log('知识点数据尚未初始化')
    return
  }
  
  console.log('所有知识点状态:')
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      subSection.points.forEach(point => {
        // console.log(`知识点ID: ${point.id}, 名称: ${point.name}, 状态: ${getStatusText(point.status)}(${point.status}), isHotspot: ${point.isHotspot}`)
      })
    })
  })
}

// 更新知识点状态
const updatePointStatus = (pointId: string, newStatus: number) => {
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      const point = subSection.points.find(p => p.id === pointId)
      if (point) {
        point.status = newStatus
        
        // 根据状态更新isHotspot
        if (newStatus === 0) {
          point.isHotspot = true
        } else if (newStatus === -1) {
          point.isHotspot = false
        }
        
        console.log(`已更新知识点(${pointId})状态为: ${getStatusText(newStatus)}(${newStatus})`)
      }
    })
  })
  
  // 记录更新后的状态
  logAllPointsStatus()
}

// 更新所有知识点状态
const updateAllPointsStatus = (newStatus: number) => {
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      subSection.points.forEach(point => {
        point.status = newStatus
        
        // 根据状态更新isHotspot
        if (newStatus === 0) {
          point.isHotspot = true
        } else if (newStatus === -1) {
          point.isHotspot = false
        }
      })
    })
  })
  
  console.log(`已将所有知识点状态更新为: ${getStatusText(newStatus)}(${newStatus})`)
  // 记录更新后的状态
  logAllPointsStatus()
}

// 同步所有知识点的isHotspot和status
const syncAllPointsHotspotStatus = () => {
  // 确保knowledgeSections已初始化
  if (!knowledgeSections.value || knowledgeSections.value.length === 0) {
    return
  }
  
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      subSection.points.forEach(point => {
        // 只有status为0时，isHotspot才为true
        point.isHotspot = point.status === 0
      })
    })
  })
  console.log('已同步所有知识点的isHotspot和status')
}

// 检查是否有重复的知识点ID
const checkDuplicatePointIds = () => {
  // 确保knowledgeSections已初始化
  if (!knowledgeSections.value || knowledgeSections.value.length === 0) {
    return false
  }
  
  // 收集所有知识点ID
  const allIds: string[] = []
  const duplicateIds: string[] = []
  
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      subSection.points.forEach(point => {
        if (point.id) {
          if (allIds.includes(point.id)) {
            duplicateIds.push(point.id)
          } else {
            allIds.push(point.id)
          }
        }
      })
    })
  })
  
  if (duplicateIds.length > 0) {
    // console.warn('发现重复的知识点ID:', duplicateIds)
    return true
  } else {
    // console.log('没有发现重复的知识点ID')
    return false
  }
}

// 确保知识点ID唯一性
const ensureUniquePointIds = () => {
  // 确保knowledgeSections已初始化
  if (!knowledgeSections.value || knowledgeSections.value.length === 0) {
    return 0
  }
  
  const idMap = new Map<string, boolean>()
  let duplicateCount = 0
  
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      subSection.points.forEach((point, index) => {
        if (!point.id) {
          // 为没有ID的知识点生成一个唯一ID
          point.id = `generated-${section.id}-${index}-${Date.now()}`
        } else if (idMap.has(point.id)) {
          // 发现重复ID，但不修改，只记录
          duplicateCount++
          console.log(`发现重复ID: ${point.id}，保留原ID不修改`)
        }
        
        // 记录此ID已被使用
        idMap.set(point.id, true)
      })
    })
  })
  
  if (duplicateCount > 0) {
    console.log(`发现${duplicateCount}个重复ID，保留原ID不修改`)
  } else {
    console.log('所有知识点ID已唯一')
  }
  return duplicateCount
}
</script>

<style scoped>
.knowledge-hotspot-page {
  width: 100%;
  min-height: 100vh;
  background-color: #fff;
  padding: 20px;
}

.rule-notice {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  padding: 12px 0 12px 0;
  border-radius: 8px;
}

.notice-icon {
  width: 14px;
  height: 14px;
  background-image: url('@/assets/img/percision/standard-help.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-right: 8px;
  flex-shrink: 0;
}

.notice-text {
  font-size: 12px;
  color: rgba(90, 133, 236, 1);
  line-height: 1.4;
}

.knowledge-sections {
  margin-bottom: 80px;
}

.knowledge-section {
  /* background-color: #fff; */
  border-radius: 8px;
  margin-bottom: 16px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
  overflow: hidden;
}

/* 一级标题样式 - 不可折叠 */
.section-header {
  padding: 10px 0;
  /* background-color: #f5f7f9;
  border-bottom: 1px solid #eaeaea; */
}

.section-title {
  font-size: 16px;
  font-weight: 700;
  color: #2a2b2a;
  margin: 0;
}

.section-content {
  padding: 0;
}

/* 二级标题样式 */
.sub-section {
  border-bottom: 1px solid #eaeaea;
}

.sub-section:last-child {
  border-bottom: none;
}

.sub-section-header {
  box-sizing: border-box;
  border-bottom: 1px solid rgba(234, 234, 234, 1);
  background: rgba(245, 247, 249, 1);
  padding: 20px 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.3s ease;
}

.sub-section-header:hover {
  background-color: #f8f9fa;
}

.sub-section-title {
  font-size: 16px;
  font-weight: 400;
  color: #2a2b2a;
  margin: 0;
}

.toggle-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: url('@/assets/img/percision/standard-down.png');
  background-size: cover;
}

.toggle-icon.expanded {
  background: url('@/assets/img/percision/standard-up.png');
  background-size: cover;
}

.arrow {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #fff;
  transition: transform 0.3s ease;
}

.toggle-icon:not(.expanded) .arrow {
  background: url('@/assets/img/percision/standard-up.png');
  background-size: cover;
}

/* 知识点内容区域 */
.sub-section-content {
  /* padding: 20px; */
  /* background-color: #fafbfc; */
  /* margin-bottom: 20px; */
  border-radius: 0;
  overflow: hidden;
}

.knowledge-points-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1px;
  background-color: rgba(234, 234, 234, 1);
  padding: 1px;
}

.knowledge-point-item {
  background-color: #fff;
  padding: 16px 20px;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 72px;
  box-sizing: border-box;
  margin: 0;
}

/* .knowledge-point-item:hover { */
  /* border-color: #5a85ec; */
  /* box-shadow: 0 2px 8px rgba(90, 133, 236, 0.1); */
/* } */

.knowledge-point-item.is-hotspot {
  background: rgba(255, 248, 240, 1);
}

.knowledge-point-item.status-marked {
  background: rgba(255, 248, 240, 1);
}

.knowledge-point-item.status-mastered {
  background: rgba(240, 255, 244, 1);
}

.knowledge-point-item.status-unmastered {
  background: rgba(255, 240, 240, 1);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-icon {
  width: 70px;
  height: 34px;
  object-fit: contain;
}

/* 已标记状态 */
.status-marked .status-text {
  color: rgba(254, 129, 62, 1);
}

/* 已掌握状态 */
.status-mastered .status-text {
  color: rgba(82, 196, 26, 1);
}

/* 未掌握状态 */
.status-unmastered .status-text {
  color: rgba(245, 34, 45, 1);
}

.point-content {
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.point-name {
  color: #2a2b2a;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 260px;
  font-size: 14px;
}
.understood{
  width: 70px;
  height: 34px;
}
.point-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-shrink: 0;
  min-width: 80px;
  justify-content: flex-end;
}

.mark-btn {
  display: flex;
  align-items: center;
  /* gap: 4px; */
  /* padding: 6px 12px; */
  background: none;
  border: none;
  color: #5A85EC;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  font-size: 14px;
  white-space: nowrap;
}

.mark-btn:hover {
  background-color: rgba(90, 133, 236, 0.1);
}

.hotspot-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
  white-space: nowrap;
}

.star-icon {
  width: 14px;
  height: 14px;
  background-image: url('@/assets/img/percision/standard-star.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-right: 4px;
}

.star-icon.active {
  width: 20px;
  height: 16px;
  background-image: url('@/assets/img/percision/standard-stared.png');
  background-size: 100% 100%;
}

.status-text {
  font-size: 12px;
  color: rgba(254, 129, 62, 1);
  font-weight: 500;
}

.cancel-btn {
  background: none;
  border: 0;
  color: rgba(153, 153, 153, 1);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.cancel-btn:hover {
  background-color: #ff4d4f;
  color: white;
}

.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  /* background-color: #fff; */
  display: flex;
  align-items: center;
  justify-content: center;
  /* box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); */
}

.action-button {
  width: 253px;
  height: 75px;
  line-height: 60px;
  display: flex;
  /* align-items: center; */
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  background: url('@/assets/img/percision/standard-btn.png')center center no-repeat;
  background-size: cover;
}

.action-button.disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.button-text {
  font-size: 16px;
  font-weight: 500;
}

.empty-points-message {
  padding: 20px;
  text-align: center;
  color: #999;
  font-size: 14px;
  background-color: #f9f9f9;
  border-radius: 0;
  grid-column: 1 / -1;
}

/* 加载遮罩层样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #5a85ec;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式布局 */
@media screen and (max-width: 1200px) {
  .knowledge-points-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1px;
  }
}

@media screen and (max-width: 768px) {
  .knowledge-points-grid {
    grid-template-columns: 1fr;
    gap: 1px;
  }
  
  .point-name {
    max-width: none;
  }
}
</style>

