<!-- 我的积分 -->
<template>
    <div class="content" v-loading="loading">
        <div class="header">
            <div class="header-img">
                <img src="@/assets/img/percision/back.png" alt="back" @click="goBack"></img>
                <img src="@/assets/img/percision/final_text.png" alt="text"></img>
            </div>
            <div class="header-text">
                {{ subjectObj.editionName }}{{ subjectObj.typeName }}
            </div>
        </div>
        <div class="main">
            <img class="record-sty" @click="goRecord" src="@/assets/img/percision/record.png" alt="record"></img>
            <div class="main-box">
                <div class="main-box-flex">
                    <img class="way-start" src="@/assets/img/percision/final_start.png" alt="start"></img>
                    <div v-for="(item, index) in options">
                        <div v-if="index == 0" class="way-box">
                            <img class="way-sty" src="@/assets/img/percision/way1.png" alt=""></img>
                            <div class="island-box position-right">
                                <img class="way-sty" :src="getIsland(index, item.status)" alt=""></img>
                                <img v-if="item.status == 1" class="way-status" src="@/assets/img/percision/final_success.png" alt=""></img>
                                <div v-if="item.status !== 0" class="way-rate-box">
                                    <img src="@/assets/img/percision/report.png" alt=""></img>
                                    正确率：<span class="way-rate">{{ item.correctRate }}</span>%
                                </div>
                                <div class="way-point-box">
                                    <div class="way-point-box-cont">
                                        {{ index + 1 }}. {{ item.pointName }}
                                    </div>
                                    <div v-if="item.status == 0" class="start-btn-box">
                                        <img @click="reChallenge(item)" src="@/assets/img/percision/start-btn.png" alt=""></img>
                                    </div>
                                    <div v-if="item.status == 2" class="way-point-box-btn">
                                        <div @click="reChallenge(item)">再次闯关</div>
                                        <div @click="goLearning(item)"><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else-if="index % 2 !== 0" class="way-box">
                            <img class="way-sty" src="@/assets/img/percision/way2.png" alt=""></img>
                            <div class="island-box position-left top50">
                                <img class="way-sty" :src="getIsland(index, item.status)" alt=""></img>
                                <img v-if="item.status == 1" class="way-status" src="@/assets/img/percision/final_success.png" alt=""></img>
                                <img v-if="showChain(item.status, index)" class="way-status-chain" src="@/assets/img/percision/chain.png" alt=""></img>
                                <div v-if="item.status !== 0" class="way-rate-box text-right">
                                    <img src="@/assets/img/percision/report.png" alt=""></img>
                                    正确率：<span class="way-rate">{{ item.correctRate }}</span>%
                                </div>
                                <div class="way-point-box">
                                    <div class="way-point-box-cont">
                                        {{ index + 1 }}. {{ item.pointName }}
                                    </div>
                                    <div v-if="showStart(item.status, index)" class="start-btn-box">
                                        <img @click="reChallenge(item)" src="@/assets/img/percision/start-btn.png" alt=""></img>
                                    </div>
                                    <div v-if="item.status == 2" class="way-point-box-btn">
                                        <div @click="reChallenge(item)">再次闯关</div>
                                        <div @click="goLearning(item)"><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else-if="index % 2 === 0" class="way-box">
                            <img class="way-sty" src="@/assets/img/percision/way3.png" alt=""></img>
                            <div class="island-box position-right top50">
                                <img class="way-sty" :src="getIsland(index, item.status)" alt=""></img>
                                <img v-if="item.status == 1" class="way-status" src="@/assets/img/percision/final_success.png" alt=""></img>
                                <img v-if="showChain(item.status, index)" class="way-status-chain" src="@/assets/img/percision/chain.png" alt=""></img>
                                <div v-if="item.status !== 0" class="way-rate-box">
                                    <img src="@/assets/img/percision/report.png" alt=""></img>
                                    正确率：<span class="way-rate">{{ item.correctRate }}</span>%
                                </div>
                                <div class="way-point-box">
                                    <div class="way-point-box-cont">
                                        {{ index + 1 }}. {{ item.pointName }}
                                    </div>
                                    <div v-if="showStart(item.status, index)" class="start-btn-box">
                                        <img @click="reChallenge(item)" src="@/assets/img/percision/start-btn.png" alt=""></img>
                                    </div>
                                    <div v-if="item.status == 2" class="way-point-box-btn">
                                        <div @click="reChallenge(item)">再次闯关</div>
                                        <div @click="goLearning(item)"><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <PassGifDialog ref="passGifDialog" />
</template>
  
<script lang="ts" setup>
import router from '@/router'
import { dataEncrypt } from '@/utils/secrets'
import PassGifDialog from '@/views/ai_percision/finalQuestion/components/pass_gif_dialog.vue'
import { onMounted, ref } from 'vue'
import { getFinalePointApi } from "@/api/point"
import { useUserStore } from "@/store/modules/user"
import { storeToRefs } from 'pinia'
const passGifDialog = ref()
const loading = ref(false)
const userStore = useUserStore()

let { subjectObj, learnNow,chapterObj } = storeToRefs(userStore)
const options = ref([] as any[])
onMounted(() => {
    getPointlist()
})
const getPointlist = async() => { 
    loading.value = true
    const res: any = await  getFinalePointApi({
        bookId: subjectObj.value.bookId
    })
    if(res.code == 200) {
      options.value = res.data || []
      if (res.data[res.data.length - 1].status3 == 1) {
        passGifDialog.value.init()
      }
    }
    loading.value = false
}
const showChain = (status: number, index: number) => {
    if (status == 0) {
        if (options.value[index - 1].status == 1) {
            return false
        } else {
            return true
        }
    } else{
        return false
    }
}
const showStart = (status: number, index: number) => {
    if (status == 0) {
        if (options.value[index - 1].status == 1) {
            return true
        } else {
            return false
        }
    } else{
        return false
    }
}
const goBack = () => {
    router.push({
        path: '/ai_percision/knowledge_graph'
    })
}
const getIsland = (index: number, status: number) => {
    const sign = index % 4
    const lastStaus = options.value[index - 1]?options.value[index - 1].status: null
    let img = ""
    if (status == 1 || (lastStaus == 1 && status == 0) || lastStaus == null) {
        switch (sign) {
            case 0:
                img = "island1.png"
                break
            case 1:
                img = "island2.png"
                break
            case 2:
                img = "island3.png"
                break
            case 3:
                img = "island4.png"
                break
        }
    } else {
        switch (sign) {
            case 0:
                img = "island1_grey.png"
                break
            case 1:
                img = "island2_grey.png"
                break
            case 2:
                img = "island3_grey.png"
                break
            case 3:
                img = "island4_grey.png"
                break
        }
    }
    return new URL(`../../../assets/img/percision/${img}`, import.meta.url).href //静态资源引入为url，相当于require()

}
const reChallenge = (data: any) => { 
    router.push({
        path: '/ai_percision/final_question/final_question_write',
        query: {
            data: dataEncrypt({
                pointId: data.pointId,
                pageSource: '11'
            })
        }
    })
}
const goRecord = () => { 
    router.push({
        path: '/ai_percision/final_question/final__record',
        query: {
            data: dataEncrypt({
                pageSource: '11'
            })
        }
    })
}

const goLearning = (data: any) => { 
    router.push({
        path: '/ai_percision/final_question/final__learning',
        query: {
            data: dataEncrypt({
                // sourceId: chapterId.value,
                // chapterId: chapterId.value,
                pointId: [data.pointId],
                subject: subjectObj.value.id
            })
        }
    })
}
</script>
  
<style lang="scss" scoped>
.content{
    width: 100%;
    height: calc(100vh - 70px);
    background: url(@/assets/img/percision/finalbg.png) no-repeat;
    background-size: 100% calc(100vh - 70px);
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: column;
    .header {
        width: 1300px;
        height: 93px;
        display: flex;
        padding-top: 30px;
        box-sizing: border-box;
        justify-content: space-between;
        &-img {
            display: flex;
            align-items: center;
            img:first-child {
                width: 44px;
                height: 32px;
                margin-right: 16px;
                cursor: pointer;
            }
            img:last-child {
                width: 205px;
                height: 53px;
            }
        }
        &-text {
            height: fit-content;
            border-radius: 22px;
            background: #0000004d;
            color: #ffffff;
            font-size: 16px;
            font-weight: 400;
            padding: 11px 20px;
        }
    }
    .main {
        width: 1300px;
        height: calc(100vh - 163px);
        background: #ffffff;
        padding: 20px 20px 0 20px;
        box-sizing: border-box;
        position: relative;
        .record-sty {
            position: absolute;
            bottom: 50px;
            right: 70px;
            width: 87px;
            height: 80px;
            cursor: pointer;
        }
        &-box {
            height: calc(100vh - 183px);
            width: 100%;
            background: url(@/assets/img/percision/finalbf2.png) no-repeat;
            background-size: 100% 100%;
            display: flex;
            overflow-y: auto;
            justify-content: center;
            .main-box-flex {
                width: 684px;
                margin-top: 10px;
                padding: 0 76px;
                box-sizing: border-box;
            }
            .way-start {
                width: 97px;
                height: 65px;
                margin-left: 26px;
                margin-bottom: -5px;
            }
            .way-box {
                position: relative;
                .way-sty {
                    width: 534px;
                }
                .island-box {
                    position: absolute;
                    top: 0;
                    .way-sty {
                        width: 200px;
                        height: 200px;
                        position: relative;
                        z-index: 10;
                    }
                    .way-status {
                        position: absolute;
                        top: -50px;
                        left: 0;
                        width: 200px;
                        height: 200px;
                        z-index: 11;
                    }
                    .way-status-chain {
                        position: absolute;
                        width: 180px;
                        left: 8px;
                        top: 60px;
                        z-index: 100;
                    }
                    .way-rate-box {
                        position: absolute;
                        z-index: 1;
                        width: 180px;
                        top: 90px;
                        right: 150px;
                        background-color: #ffffff;
                        padding: 6px 12px;
                        font-size: 14px;
                        border-radius: 16px;
                        display: flex;
                        align-items: center;
                        span {
                            font-weight: 700;
                        }
                        img {
                            width: 16px;
                            height: 16px;
                            margin-right: 5px;
                        }
                    }
                    .text-right {
                        left: 150px;
                        width: 120px;
                        padding-left: 60px;
                    }
                    .way-point-box {
                        position: absolute;
                        top: 143px;
                        z-index: 20;
                        &-cont {
                            padding: 2px 12px;
                            width: 200px;
                            text-align: center;
                            box-sizing: border-box;
                            min-height: 30px;
                            border-radius: 10px;
                            border: 2px solid #5a85ec;
                            background: #ffffffcc;
                            color: #323a57;
                        }
                        &-btn {
                            display: flex;
                            justify-content: space-between;
                            margin-top: 6px;
                            div {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 90px;
                                cursor: pointer;
                                height: 29px;
                                border-radius: 14.5px;
                                border: 2px solid #f25500;
                                background: #e98b00;
                                color: #ffffff;
                                font-size: 14px;
                                img {
                                    width: 16px;
                                    height: 16px;
                                    margin-right: 5px;
                                }
                            }
                        }
                    }
                }
                .position-right {
                    right: -100px;
                }
                .position-left {
                    left: -100px;
                }
                .top50 {
                    top: 58px;
                }
            }
        }
    }
}
.start-btn-box {
    width: 200px;
    display: flex;
    justify-content: center;
    margin-top: 6px;
    img {
        width: 114px;
        height: 42px;
    }
}
</style>
  